<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG轉PNG工具 - Tabbar圖標生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #5a9178;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .icon-item {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #fafafa;
        }
        .icon-preview {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 20px 0;
        }
        .icon-size {
            text-align: center;
        }
        .icon-size span {
            display: block;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        svg {
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        button {
            background: #5a9178;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background: #4a7d68;
        }
        .batch-download {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .batch-download button {
            background: #007aff;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: bold;
        }
        .batch-download button:hover {
            background: #0056cc;
        }
        .instructions {
            background: #f0f8ff;
            border-left: 4px solid #007aff;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Tabbar圖標PNG生成器</h1>
        
        <div class="instructions">
            <strong>使用說明：</strong>
            <ol>
                <li>下方顯示了所有tabbar圖標的預覽</li>
                <li>點擊單個"下載PNG"按鈕下載特定圖標</li>
                <li>點擊"批量下載所有PNG"一次性下載所有圖標</li>
                <li>生成的PNG文件可直接用於微信小程序tabbar</li>
            </ol>
        </div>

        <div class="icon-grid" id="iconGrid">
            <!-- 圖標將在這裡動態生成 -->
        </div>

        <div class="batch-download">
            <button onclick="downloadAllPNG()">📥 批量下載所有PNG圖標</button>
        </div>
    </div>

    <script>
        // 圖標數據
        const icons = {
            'home': {
                name: '首頁',
                normal: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>`,
                active: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15" stroke="#5a9178" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>`
            },
            'coach': {
                name: '教練',
                normal: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M20.5906 22C20.5906 18.13 16.7406 15 12.0006 15C7.26063 15 3.41063 18.13 3.41063 22" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 12L14 10M14 10L16 12" stroke="#999999" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>`,
                active: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" stroke="#5a9178" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M20.5906 22C20.5906 18.13 16.7406 15 12.0006 15C7.26063 15 3.41063 18.13 3.41063 22" stroke="#5a9178" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 12L14 10M14 10L16 12" stroke="#5a9178" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>`
            },
            'appointment': {
                name: '預約',
                normal: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="#999999" stroke-width="2"/>
                    <line x1="16" y1="2" x2="16" y2="6" stroke="#999999" stroke-width="2" stroke-linecap="round"/>
                    <line x1="8" y1="2" x2="8" y2="6" stroke="#999999" stroke-width="2" stroke-linecap="round"/>
                    <line x1="3" y1="10" x2="21" y2="10" stroke="#999999" stroke-width="2"/>
                    <circle cx="12" cy="15" r="2" fill="#999999"/>
                </svg>`,
                active: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="#5a9178" stroke-width="2"/>
                    <line x1="16" y1="2" x2="16" y2="6" stroke="#5a9178" stroke-width="2" stroke-linecap="round"/>
                    <line x1="8" y1="2" x2="8" y2="6" stroke="#5a9178" stroke-width="2" stroke-linecap="round"/>
                    <line x1="3" y1="10" x2="21" y2="10" stroke="#5a9178" stroke-width="2"/>
                    <circle cx="12" cy="15" r="2" fill="#5a9178"/>
                </svg>`
            },
            'user': {
                name: '我的',
                normal: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="7" r="4" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>`,
                active: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="#5a9178" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="7" r="4" stroke="#5a9178" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>`
            }
        };

        // SVG轉PNG函數
        function svgToPng(svgString, size, callback) {
            const svg = new DOMParser().parseFromString(svgString, 'image/svg+xml').documentElement;
            svg.setAttribute('width', size);
            svg.setAttribute('height', size);
            
            const svgData = new XMLSerializer().serializeToString(svg);
            const img = new Image();
            
            img.onload = function() {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');
                
                // 設置白色背景（可選）
                // ctx.fillStyle = 'white';
                // ctx.fillRect(0, 0, size, size);
                
                ctx.drawImage(img, 0, 0, size, size);
                canvas.toBlob(callback, 'image/png');
            };
            
            img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
        }

        // 下載單個PNG
        function downloadPNG(iconKey, state, size) {
            const svgString = icons[iconKey][state];
            const filename = `${iconKey}${state === 'active' ? '-active' : ''}.png`;
            
            svgToPng(svgString, size, function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }

        // 批量下載所有PNG
        function downloadAllPNG() {
            const sizes = [64]; // 小程序推薦尺寸
            let downloadCount = 0;
            const totalDownloads = Object.keys(icons).length * 2 * sizes.length;
            
            Object.keys(icons).forEach(iconKey => {
                ['normal', 'active'].forEach(state => {
                    sizes.forEach(size => {
                        setTimeout(() => {
                            downloadPNG(iconKey, state, size);
                            downloadCount++;
                            if (downloadCount === totalDownloads) {
                                alert('所有PNG圖標下載完成！');
                            }
                        }, downloadCount * 200); // 延遲下載避免瀏覽器阻擋
                    });
                });
            });
        }

        // 生成圖標預覽
        function generateIconPreviews() {
            const grid = document.getElementById('iconGrid');
            
            Object.keys(icons).forEach(iconKey => {
                const icon = icons[iconKey];
                const div = document.createElement('div');
                div.className = 'icon-item';
                
                div.innerHTML = `
                    <h3>${icon.name} (${iconKey})</h3>
                    <div class="icon-preview">
                        <div class="icon-size">
                            ${icon.normal}
                            <span>普通狀態</span>
                        </div>
                        <div class="icon-size">
                            ${icon.active}
                            <span>激活狀態</span>
                        </div>
                    </div>
                    <div>
                        <button onclick="downloadPNG('${iconKey}', 'normal', 64)">下載普通PNG</button>
                        <button onclick="downloadPNG('${iconKey}', 'active', 64)">下載激活PNG</button>
                    </div>
                `;
                
                grid.appendChild(div);
            });
        }

        // 頁面載入時生成預覽
        document.addEventListener('DOMContentLoaded', generateIconPreviews);
    </script>
</body>
</html> 