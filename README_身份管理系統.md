# Coach-Q 小程序身份管理系統

## 功能概述

Coach-Q 小程序實現了教練和學員身份的嚴格區分管理，確保用戶在註冊時選擇的身份與登錄後的身份保持一致，並且不支持身份切換。

## 主要功能特點

### 1. 註冊流程身份選擇
- 用戶在註冊時必須選擇身份類型：**學員** 或 **教練**
- 身份選擇會直接保存到用戶信息中
- 不同身份有不同的註冊說明和功能提示

### 2. 登錄自動身份識別
- 系統會根據用戶的手機號自動從服務器獲取已註冊的身份類型
- 登錄成功後會自動設置對應的用戶權限和功能菜單
- 確保登錄身份與註冊時選擇的身份一致

### 3. 個人中心身份顯示
- 清晰顯示用戶的身份標籤（學員/教練）
- 添加 "身份已確認，不支持切換" 的說明文字
- 根據身份類型動態加載不同的功能列表

## 技術實現

### 數據存儲結構
```javascript
// 用戶信息結構
userInfo: {
  id: Number,           // 用戶ID
  nickName: String,     // 用戶暱稱
  avatarUrl: String,    // 頭像URL
  phone: String,        // 手機號
  userType: String      // 用戶類型：'student' | 'coach'
}

// 本地存儲
wx.setStorageSync('token', token)
wx.setStorageSync('userInfo', userInfo)  
wx.setStorageSync('userType', userType)
```

### 核心邏輯流程

#### 註冊流程
1. 用戶選擇身份類型（學員/教練）
2. 填寫註冊信息（手機號、密碼、驗證碼）
3. 提交註冊請求，將身份類型一併保存
4. 註冊成功後自動登錄並設置對應權限

#### 登錄流程  
1. 輸入手機號和驗證碼
2. 系統根據手機號查詢用戶身份類型
3. 登錄成功後設置用戶信息和權限
4. 跳轉到首頁，個人中心顯示對應身份功能

#### 個人中心顯示
1. 檢查登錄狀態和用戶身份
2. 根據身份類型加載對應功能列表：
   - **學員**：我的預約、我的課程、我的訂單、意見反饋
   - **教練**：預約管理、課程管理、學員管理、收入統計
3. 顯示身份標籤和不可切換說明

## 頁面結構

### 註冊頁面 (`pages/register/register`)
- 身份選擇器（學員/教練）
- 註冊表單（手機號、驗證碼、密碼）
- 教練註冊須知（教練身份時顯示）

### 登錄頁面 (`pages/login/login`)  
- 手機號登錄
- 自動身份識別邏輯
- 微信登錄支持

### 個人中心 (`pages/user/index`)
- 用戶信息展示（含身份標籤）
- 身份相關功能列表
- 設置選項
- 退出登錄

## 關鍵配置

### 全局數據 (`app.js`)
```javascript
globalData: {
  userInfo: null,        // 用戶信息
  isLogin: false,        // 登錄狀態
  userType: '',          // 用戶類型
  baseUrl: 'https://api.example.com'  // API基礎地址
}
```

### 身份判斷邏輯
```javascript
// 模擬從服務器獲取用戶類型
getUserTypeFromServer(phone) {
  // 實際項目中應該調用真實API
  const lastDigit = parseInt(phone.substr(-1));
  return lastDigit % 2 === 0 ? 'student' : 'coach';
}
```

## 安全考慮

1. **身份驗證**：所有身份相關操作都需要驗證token
2. **數據一致性**：確保本地存儲和服務器數據的一致性  
3. **權限控制**：不同身份只能訪問對應的功能頁面
4. **防篡改**：身份信息存儲在服務器端，客戶端僅做展示

## 未來擴展

1. **身份認證**：教練身份可能需要資質認證
2. **多角色**：可能支持管理員等其他角色
3. **身份升級**：學員升級為教練的申請流程
4. **企業用戶**：支持機構/俱樂部等企業用戶類型

## 注意事項

- 身份類型一旦選擇註冊後不可更改
- 登錄時會自動同步最新的用戶身份信息
- 功能列表會根據身份動態調整
- 確保API接口支持身份類型的獲取和驗證 