<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG 图标转换器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2 {
            color: #333;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
        }
        .icon-preview {
            width: 48px;
            height: 48px;
            margin-bottom: 10px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .icon-preview svg {
            width: 24px;
            height: 24px;
            color: #333;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .settings {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
        }
        .settings label {
            display: block;
            margin-bottom: 10px;
        }
        .settings input, .settings select {
            margin-left: 10px;
        }
        .download-all {
            background-color: #2196F3;
        }
        .download-all:hover {
            background-color: #0b7dda;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>SVG 图标转换器</h1>
    <p>此工具可以将 SVG 图标转换为 PNG 格式。</p>
    
    <div class="settings">
        <h2>设置</h2>
        <label>
            图标大小:
            <input type="number" id="iconSize" value="32" min="16" max="256">px
        </label>
        <label>
            图标颜色:
            <input type="color" id="iconColor" value="#000000">
        </label>
        <label>
            背景颜色:
            <input type="color" id="bgColor" value="#ffffff">
            <input type="checkbox" id="transparentBg" checked> 透明背景
        </label>
    </div>
    
    <div class="controls">
        <button id="loadIcons">加载图标</button>
        <button id="downloadAll" class="download-all">下载所有图标</button>
    </div>
    
    <div id="status" class="status">请点击"加载图标"按钮开始。</div>
    
    <div id="iconGrid" class="icon-grid"></div>
    
    <script>
        // 图标列表
        const iconIds = [
            'arrow-right',
            'profile',
            'notification',
            'privacy',
            'agreement',
            'switch-identity',
            'appointment',
            'course',
            'order',
            'feedback',
            'appointment-manage',
            'course-manage',
            'student-manage',
            'income'
        ];
        
        // 加载 SVG 文件
        async function loadSvgFile() {
            try {
                const response = await fetch('images/icon/icons.svg');
                const svgText = await response.text();
                const parser = new DOMParser();
                const svgDoc = parser.parseFromString(svgText, 'image/svg+xml');
                return svgDoc;
            } catch (error) {
                console.error('加载 SVG 文件失败:', error);
                document.getElementById('status').textContent = '加载 SVG 文件失败: ' + error.message;
                return null;
            }
        }
        
        // 显示图标
        function displayIcons(svgDoc) {
            const iconGrid = document.getElementById('iconGrid');
            iconGrid.innerHTML = '';
            
            iconIds.forEach(id => {
                const symbol = svgDoc.getElementById(id);
                if (!symbol) {
                    console.warn(`未找到图标: ${id}`);
                    return;
                }
                
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                
                const iconPreview = document.createElement('div');
                iconPreview.className = 'icon-preview';
                
                // 创建 SVG 元素
                const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svg.setAttribute('width', '24');
                svg.setAttribute('height', '24');
                svg.setAttribute('viewBox', symbol.getAttribute('viewBox'));
                
                // 复制路径
                const paths = symbol.querySelectorAll('path');
                paths.forEach(path => {
                    const newPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    newPath.setAttribute('d', path.getAttribute('d'));
                    newPath.setAttribute('fill', 'currentColor');
                    svg.appendChild(newPath);
                });
                
                iconPreview.appendChild(svg);
                
                const iconName = document.createElement('div');
                iconName.textContent = id;
                
                const downloadButton = document.createElement('button');
                downloadButton.textContent = '下载';
                downloadButton.onclick = () => downloadIcon(id, svg);
                
                iconItem.appendChild(iconPreview);
                iconItem.appendChild(iconName);
                iconItem.appendChild(downloadButton);
                
                iconGrid.appendChild(iconItem);
            });
            
            document.getElementById('status').textContent = '图标加载完成，可以下载了。';
        }
        
        // 下载单个图标
        function downloadIcon(id, svgElement) {
            const size = parseInt(document.getElementById('iconSize').value);
            const color = document.getElementById('iconColor').value;
            const bgColor = document.getElementById('transparentBg').checked ? 'transparent' : document.getElementById('bgColor').value;
            
            // 创建一个新的 SVG 元素，设置大小和颜色
            const newSvg = svgElement.cloneNode(true);
            newSvg.setAttribute('width', size);
            newSvg.setAttribute('height', size);
            newSvg.querySelectorAll('path').forEach(path => {
                path.setAttribute('fill', color);
            });
            
            // 转换为 SVG 字符串
            const svgString = new XMLSerializer().serializeToString(newSvg);
            const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
            const svgUrl = URL.createObjectURL(svgBlob);
            
            // 创建 Canvas
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 设置背景色
            if (bgColor !== 'transparent') {
                ctx.fillStyle = bgColor;
                ctx.fillRect(0, 0, size, size);
            }
            
            // 加载 SVG 到 Image 对象
            const img = new Image();
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                // 转换为 PNG 并下载
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${id}.png`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 'image/png');
                
                URL.revokeObjectURL(svgUrl);
            };
            img.src = svgUrl;
        }
        
        // 下载所有图标
        async function downloadAllIcons() {
            const svgDoc = await loadSvgFile();
            if (!svgDoc) return;
            
            document.getElementById('status').textContent = '正在准备下载所有图标...';
            
            let counter = 0;
            const total = iconIds.length;
            
            for (const id of iconIds) {
                const symbol = svgDoc.getElementById(id);
                if (!symbol) {
                    console.warn(`未找到图标: ${id}`);
                    continue;
                }
                
                // 创建 SVG 元素
                const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svg.setAttribute('viewBox', symbol.getAttribute('viewBox'));
                
                // 复制路径
                const paths = symbol.querySelectorAll('path');
                paths.forEach(path => {
                    const newPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    newPath.setAttribute('d', path.getAttribute('d'));
                    newPath.setAttribute('fill', 'currentColor');
                    svg.appendChild(newPath);
                });
                
                // 下载图标
                await new Promise(resolve => {
                    setTimeout(() => {
                        downloadIcon(id, svg);
                        counter++;
                        document.getElementById('status').textContent = `正在下载图标 ${counter}/${total}...`;
                        resolve();
                    }, 500); // 添加延迟，避免浏览器阻止多次下载
                });
            }
            
            document.getElementById('status').textContent = `所有图标下载完成 (${counter}/${total})`;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('loadIcons').addEventListener('click', async function() {
                document.getElementById('status').textContent = '正在加载图标...';
                const svgDoc = await loadSvgFile();
                if (svgDoc) {
                    displayIcons(svgDoc);
                }
            });
            
            document.getElementById('downloadAll').addEventListener('click', downloadAllIcons);
        });
    </script>
</body>
</html>