const { createCanvas } = require('canvas');
const fs = require('fs');
const path = require('path');

// 确保目录存在
const iconDir = path.join(__dirname, 'images', 'tab');
if (!fs.existsSync(iconDir)) {
  fs.mkdirSync(iconDir, { recursive: true });
}

// 图标配置
const icons = [
  {
    name: 'home',
    draw: (ctx, color) => {
      // 绘制房子图标
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      
      // 房子屋顶
      ctx.beginPath();
      ctx.moveTo(10, 15);
      ctx.lineTo(25, 5);
      ctx.lineTo(40, 15);
      ctx.stroke();
      
      // 房子主体
      ctx.beginPath();
      ctx.rect(15, 15, 20, 20);
      ctx.stroke();
      
      // 门
      ctx.beginPath();
      ctx.rect(22, 25, 6, 10);
      ctx.stroke();
    }
  },
  {
    name: 'coach',
    draw: (ctx, color) => {
      // 绘制教练图标（人物）
      ctx.strokeStyle = color;
      ctx.fillStyle = color;
      ctx.lineWidth = 2;
      
      // 头部
      ctx.beginPath();
      ctx.arc(25, 12, 7, 0, Math.PI * 2);
      ctx.stroke();
      
      // 身体
      ctx.beginPath();
      ctx.moveTo(25, 19);
      ctx.lineTo(25, 32);
      ctx.stroke();
      
      // 手臂
      ctx.beginPath();
      ctx.moveTo(25, 22);
      ctx.lineTo(15, 28);
      ctx.stroke();
      
      ctx.beginPath();
      ctx.moveTo(25, 22);
      ctx.lineTo(35, 28);
      ctx.stroke();
      
      // 腿
      ctx.beginPath();
      ctx.moveTo(25, 32);
      ctx.lineTo(18, 42);
      ctx.stroke();
      
      ctx.beginPath();
      ctx.moveTo(25, 32);
      ctx.lineTo(32, 42);
      ctx.stroke();
    }
  },
  {
    name: 'appointment',
    draw: (ctx, color) => {
      // 绘制预约图标（日历）
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      
      // 日历外框
      ctx.beginPath();
      ctx.rect(10, 10, 30, 30);
      ctx.stroke();
      
      // 日历顶部
      ctx.beginPath();
      ctx.moveTo(10, 18);
      ctx.lineTo(40, 18);
      ctx.stroke();
      
      // 日历挂钩
      ctx.beginPath();
      ctx.moveTo(17, 10);
      ctx.lineTo(17, 5);
      ctx.stroke();
      
      ctx.beginPath();
      ctx.moveTo(33, 10);
      ctx.lineTo(33, 5);
      ctx.stroke();
      
      // 日历标记
      ctx.beginPath();
      ctx.arc(25, 28, 5, 0, Math.PI * 2);
      ctx.stroke();
    }
  },
  {
    name: 'user',
    draw: (ctx, color) => {
      // 绘制用户图标
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      
      // 头部
      ctx.beginPath();
      ctx.arc(25, 15, 8, 0, Math.PI * 2);
      ctx.stroke();
      
      // 身体
      ctx.beginPath();
      ctx.arc(25, 40, 15, Math.PI * 1.3, Math.PI * 1.7, true);
      ctx.stroke();
    }
  }
];

// 生成图标
const generateIcon = (name, color, suffix = '') => {
  const canvas = createCanvas(50, 50);
  const ctx = canvas.getContext('2d');
  
  // 清除画布
  ctx.clearRect(0, 0, 50, 50);
  
  // 找到对应的图标配置
  const icon = icons.find(i => i.name === name);
  if (icon) {
    icon.draw(ctx, color);
  }
  
  // 保存为PNG
  const fileName = suffix ? `${name}-${suffix}.png` : `${name}.png`;
  const filePath = path.join(iconDir, fileName);
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(filePath, buffer);
  
  console.log(`Generated: ${filePath}`);
};

// 生成所有图标
icons.forEach(icon => {
  // 普通状态 - 灰色
  generateIcon(icon.name, '#888888');
  // 激活状态 - 绿色
  generateIcon(icon.name, '#3cc51f', 'active');
});

console.log('All icons generated successfully!');