<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Bar Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .canvas-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .icon-set {
            text-align: center;
        }
        canvas {
            border: 1px solid #ddd;
            margin-bottom: 10px;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .download-all {
            background-color: #2196F3;
            margin-top: 20px;
            padding: 12px 20px;
        }
        h1, h2 {
            color: #333;
        }
        .instructions {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Tab Bar Icon Generator</h1>
    
    <div class="instructions">
        <h2>使用说明</h2>
        <p>1. 点击每个图标下方的"下载"按钮来下载单个图标</p>
        <p>2. 或者点击底部的"下载所有图标"按钮一次性下载所有图标</p>
        <p>3. 将下载的图标放到项目的 images/tab 目录中</p>
    </div>
    
    <div class="canvas-container" id="canvasContainer"></div>
    
    <button class="download-all" id="downloadAll">下载所有图标</button>

    <script>
        // 图标配置
        const icons = [
            {
                name: 'home',
                title: '首页',
                draw: (ctx, color) => {
                    // 绘制房子图标
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 2;
                    
                    // 房子屋顶
                    ctx.beginPath();
                    ctx.moveTo(10, 15);
                    ctx.lineTo(25, 5);
                    ctx.lineTo(40, 15);
                    ctx.stroke();
                    
                    // 房子主体
                    ctx.beginPath();
                    ctx.rect(15, 15, 20, 20);
                    ctx.stroke();
                    
                    // 门
                    ctx.beginPath();
                    ctx.rect(22, 25, 6, 10);
                    ctx.stroke();
                }
            },
            {
                name: 'coach',
                title: '教练',
                draw: (ctx, color) => {
                    // 绘制教练图标（人物）
                    ctx.strokeStyle = color;
                    ctx.fillStyle = color;
                    ctx.lineWidth = 2;
                    
                    // 头部
                    ctx.beginPath();
                    ctx.arc(25, 12, 7, 0, Math.PI * 2);
                    ctx.stroke();
                    
                    // 身体
                    ctx.beginPath();
                    ctx.moveTo(25, 19);
                    ctx.lineTo(25, 32);
                    ctx.stroke();
                    
                    // 手臂
                    ctx.beginPath();
                    ctx.moveTo(25, 22);
                    ctx.lineTo(15, 28);
                    ctx.stroke();
                    
                    ctx.beginPath();
                    ctx.moveTo(25, 22);
                    ctx.lineTo(35, 28);
                    ctx.stroke();
                    
                    // 腿
                    ctx.beginPath();
                    ctx.moveTo(25, 32);
                    ctx.lineTo(18, 42);
                    ctx.stroke();
                    
                    ctx.beginPath();
                    ctx.moveTo(25, 32);
                    ctx.lineTo(32, 42);
                    ctx.stroke();
                }
            },
            {
                name: 'appointment',
                title: '预约',
                draw: (ctx, color) => {
                    // 绘制预约图标（日历）
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 2;
                    
                    // 日历外框
                    ctx.beginPath();
                    ctx.rect(10, 10, 30, 30);
                    ctx.stroke();
                    
                    // 日历顶部
                    ctx.beginPath();
                    ctx.moveTo(10, 18);
                    ctx.lineTo(40, 18);
                    ctx.stroke();
                    
                    // 日历挂钩
                    ctx.beginPath();
                    ctx.moveTo(17, 10);
                    ctx.lineTo(17, 5);
                    ctx.stroke();
                    
                    ctx.beginPath();
                    ctx.moveTo(33, 10);
                    ctx.lineTo(33, 5);
                    ctx.stroke();
                    
                    // 日历标记
                    ctx.beginPath();
                    ctx.arc(25, 28, 5, 0, Math.PI * 2);
                    ctx.stroke();
                }
            },
            {
                name: 'user',
                title: '用户',
                draw: (ctx, color) => {
                    // 绘制用户图标
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 2;
                    
                    // 头部
                    ctx.beginPath();
                    ctx.arc(25, 15, 8, 0, Math.PI * 2);
                    ctx.stroke();
                    
                    // 身体
                    ctx.beginPath();
                    ctx.arc(25, 40, 15, Math.PI * 1.3, Math.PI * 1.7, true);
                    ctx.stroke();
                }
            }
        ];

        // 颜色配置
        const normalColor = '#888888';
        const activeColor = '#3cc51f';

        // 创建和绘制所有图标
        const canvasContainer = document.getElementById('canvasContainer');
        const allCanvases = [];

        icons.forEach(icon => {
            const iconSet = document.createElement('div');
            iconSet.className = 'icon-set';
            
            // 创建标题
            const title = document.createElement('h3');
            title.textContent = icon.title;
            iconSet.appendChild(title);
            
            // 创建普通状态图标
            const normalCanvas = createIconCanvas(icon, normalColor, '');
            iconSet.appendChild(normalCanvas.canvas);
            
            // 创建下载按钮
            const normalDownloadBtn = document.createElement('button');
            normalDownloadBtn.textContent = `下载 ${icon.name}.png`;
            normalDownloadBtn.onclick = () => downloadCanvas(normalCanvas.canvas, `${icon.name}.png`);
            iconSet.appendChild(normalDownloadBtn);
            
            // 创建激活状态图标
            const activeCanvas = createIconCanvas(icon, activeColor, 'active');
            iconSet.appendChild(activeCanvas.canvas);
            
            // 创建下载按钮
            const activeDownloadBtn = document.createElement('button');
            activeDownloadBtn.textContent = `下载 ${icon.name}-active.png`;
            activeDownloadBtn.onclick = () => downloadCanvas(activeCanvas.canvas, `${icon.name}-active.png`);
            iconSet.appendChild(activeDownloadBtn);
            
            canvasContainer.appendChild(iconSet);
            
            // 添加到所有画布数组
            allCanvases.push(normalCanvas, activeCanvas);
        });

        // 创建图标画布
        function createIconCanvas(icon, color, suffix) {
            const canvas = document.createElement('canvas');
            canvas.width = 50;
            canvas.height = 50;
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 50, 50);
            
            // 绘制图标
            icon.draw(ctx, color);
            
            return {
                canvas,
                name: suffix ? `${icon.name}-${suffix}.png` : `${icon.name}.png`
            };
        }

        // 下载画布为PNG
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // 下载所有图标
        document.getElementById('downloadAll').addEventListener('click', () => {
            allCanvases.forEach(canvasInfo => {
                downloadCanvas(canvasInfo.canvas, canvasInfo.name);
            });
        });
    </script>
</body>
</html>