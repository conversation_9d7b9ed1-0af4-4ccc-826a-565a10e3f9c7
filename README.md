# Shuan-Q 台球教练预约平台

一个专业的台球教练预约移动应用，支持教练和学员的完整预约流程。

## 🏗️ 项目架构

```
Shuan-Q/
├── mobile-app/                 # React Native 移动端应用
├── backend/                   # Node.js 后端API
├── shared/                    # 共享代码和类型定义
└── docs/                      # 项目文档
```

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- React Native CLI
- Android Studio / Xcode
- MySQL >= 8.0

### 安装依赖

```bash
# 安装移动端依赖
cd mobile-app
npm install

# 安装后端依赖
cd ../backend
npm install
```

### 运行项目

```bash
# 启动后端服务
cd backend
npm run dev

# 启动移动端应用
cd mobile-app
npm run android  # 或 npm run ios
```

## 📱 功能特性

### 学员端
- 🔍 教练搜索与筛选
- 📚 课程包浏览与购买
- 📅 预约排课管理
- 📊 学习进度跟踪
- 💳 在线支付

### 教练端
- 👤 个人资料管理
- 📦 课程包创建与管理
- ⏰ 时间表设置
- 📋 预约管理与核销
- 💰 收入统计

### 通用功能
- 🔐 多种登录方式
- 💬 实时消息通知
- ⭐ 评价系统
- 📍 地理位置服务

## 🛠️ 技术栈

### 前端
- React Native
- TypeScript
- Redux Toolkit
- React Navigation
- React Native Paper

### 后端
- Node.js
- Express.js
- TypeScript
- MySQL
- Prisma ORM
- JWT Authentication

### 第三方服务
- 微信支付
- 支付宝支付
- 腾讯地图
- 阿里云OSS

## 📖 开发文档

- [API文档](./docs/api/README.md)
- [数据库设计](./docs/design/database.md)
- [部署指南](./docs/deployment.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
