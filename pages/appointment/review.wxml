<!--pages/appointment/review.wxml-->
<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <block wx:else>
    <!-- 预约信息 -->
    <view class="appointment-info">
      <view class="coach-info">
        <image class="coach-avatar" src="{{appointment.coach.avatar}}" mode="aspectFill"></image>
        <view class="coach-detail">
          <view class="coach-name">{{appointment.coach.name}}</view>
          <view class="course-info">
            {{appointment.course.type}} · {{appointment.course.date}} {{appointment.course.time}}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 评价表单 -->
    <view class="review-form">
      <!-- 评分 -->
      <view class="form-section">
        <view class="section-title">课程评分</view>
        <view class="rating-container">
          <view class="rating-stars">
            <view 
              class="star-item {{index < rating ? 'active' : ''}}" 
              wx:for="{{5}}" 
              wx:key="index"
              bindtap="onRatingChange"
              data-rating="{{index + 1}}"
            >
              <text class="star-text">{{index < rating ? '★' : '☆'}}</text>
            </view>
          </view>
          <view class="rating-label">{{ratingLabels[rating - 1]}}</view>
        </view>
      </view>
      
      <!-- 评价内容 -->
      <view class="form-section">
        <view class="section-title">评价内容</view>
        <view class="content-container">
          <textarea 
            class="content-input" 
            placeholder="请分享您对本次课程的感受和建议，至少{{minContentLength}}个字" 
            maxlength="{{maxContentLength}}"
            bindinput="onContentInput"
            value="{{content}}"
          ></textarea>
          <view class="content-counter {{content.length < minContentLength ? 'warning' : ''}}">
            {{content.length}}/{{maxContentLength}}
          </view>
        </view>
      </view>
      
      <!-- 上传图片 -->
      <view class="form-section">
        <view class="section-title">上传图片 <text class="section-subtitle">(选填，最多{{maxImageCount}}张)</text></view>
        <view class="images-container">
          <!-- 已上传图片 -->
          <view 
            class="image-item" 
            wx:for="{{images}}" 
            wx:key="index"
          >
            <image 
              class="uploaded-image" 
              src="{{item}}" 
              mode="aspectFill"
              bindtap="previewImage"
              data-url="{{item}}"
            ></image>
            <view 
              class="delete-btn" 
              catchtap="deleteImage"
              data-index="{{index}}"
            >
              <image src="/images/icon-delete.png"></image>
            </view>
          </view>
          
          <!-- 上传按钮 -->
          <view 
            class="upload-btn" 
            bindtap="chooseImage"
            wx:if="{{images.length < maxImageCount}}"
          >
            <image src="/images/icon-upload.png"></image>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-container">
      <button 
        class="submit-btn {{formValid ? '' : 'disabled'}}" 
        bindtap="submitReview"
        loading="{{submitting}}"
        disabled="{{!formValid || submitting}}"
      >
        提交评价
      </button>
    </view>
    
    <!-- 提示 -->
    <view class="tips-container">
      <view class="tips-text">您的评价将帮助其他学员更好地了解教练</view>
    </view>
  </block>
</view>