// pages/appointment/detail.js
const app = getApp()

Page({
  data: {
    id: null,
    loading: true,
    
    // 预约信息
    appointment: null,
    
    // 状态文本映射
    statusText: {
      'pending': '待上课',
      'completed': '已完成',
      'cancelled': '已取消'
    },
    
    // 状态类名映射
    statusClass: {
      'pending': 'pending',
      'completed': 'completed',
      'cancelled': 'cancelled'
    },
    
    // 倒计时
    countdown: {
      days: 0,
      hours: 0,
      minutes: 0
    },
    
    // 是否显示取消确认弹窗
    showCancelDialog: false,
    
    // 取消原因
    cancelReasons: [
      '时间冲突，无法上课',
      '临时有事，需要改期',
      '对课程内容不感兴趣',
      '找到其他教练',
      '其他原因'
    ],
    selectedReason: '',
    
    // 取消中
    cancelling: false
  },

  onLoad: function (options) {
    this.setData({
      id: options.id
    })
    
    this.loadAppointmentDetail()
  },
  
  // 加载预约详情
  loadAppointmentDetail: function() {
    this.setData({ loading: true })
    
    // 模拟API请求
    setTimeout(() => {
      // 模拟数据
      const appointment = {
        id: this.data.id,
        orderNo: 'AP' + Date.now().toString().slice(-8),
        status: ['pending', 'completed', 'cancelled'][Math.floor(Math.random() * 3)],
        
        // 教练信息
        coach: {
          id: 123,
          name: '张教练',
          avatar: '/images/default-avatar.png',
          phone: '13800138000',
          level: 'senior'
        },
        
        // 课程信息
        course: {
          type: '技术提升',
          price: 300,
          date: '2024-02-20',
          time: '15:00-17:00',
          duration: 2,
          venue: '星牌台球俱乐部',
          address: '北京市朝阳区建国路88号'
        },
        
        // 支付信息
        payment: {
          amount: 600,
          method: '微信支付',
          time: '2024-02-15 14:30:00'
        },
        
        // 创建时间
        createTime: '2024-02-15 14:30:00',
        
        // 是否可以取消
        canCancel: true,
        
        // 是否可以评价
        canReview: false,
        
        // 是否已评价
        hasReviewed: false,
        
        // 评价信息
        review: null
      }
      
      // 根据状态调整可操作性
      if (appointment.status === 'completed') {
        appointment.canCancel = false
        appointment.canReview = true
        
        // 随机生成是否已评价
        appointment.hasReviewed = Math.random() > 0.5
        
        // 如果已评价，生成评价信息
        if (appointment.hasReviewed) {
          appointment.review = {
            rating: 4.5,
            content: '教练很专业，讲解细致，课程安排合理，学到了很多。',
            images: ['/images/review-image.png'],
            createTime: '2024-02-21 10:20:00'
          }
        }
      } else if (appointment.status === 'cancelled') {
        appointment.canCancel = false
        appointment.canReview = false
      }
      
      this.setData({
        loading: false,
        appointment
      })
      
      // 如果是待上课状态，计算倒计时
      if (appointment.status === 'pending') {
        this.calculateCountdown(appointment.course.date, appointment.course.time.split('-')[0])
      }
    }, 1000)
    
    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/appointment/detail`,
      method: 'GET',
      data: { id: this.data.id },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          const appointment = res.data.data
          
          this.setData({
            loading: false,
            appointment
          })
          
          // 如果是待上课状态，计算倒计时
          if (appointment.status === 'pending') {
            this.calculateCountdown(appointment.course.date, appointment.course.time.split('-')[0])
          }
        }
      }
    })
    */
  },
  
  // 计算倒计时
  calculateCountdown: function(dateStr, timeStr) {
    const now = new Date()
    const [year, month, day] = dateStr.split('-').map(Number)
    const [hour, minute] = timeStr.split(':').map(Number)
    
    const targetDate = new Date(year, month - 1, day, hour, minute)
    const diff = targetDate - now
    
    if (diff <= 0) {
      this.setData({
        countdown: {
          days: 0,
          hours: 0,
          minutes: 0
        }
      })
      return
    }
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    this.setData({
      countdown: {
        days,
        hours,
        minutes
      }
    })
    
    // 每分钟更新一次倒计时
    setTimeout(() => {
      this.calculateCountdown(dateStr, timeStr)
    }, 60000)
  },
  
  // 跳转到教练详情
  goToCoachDetail: function() {
    const coachId = this.data.appointment.coach.id
    wx.navigateTo({
      url: `/pages/coach/detail?id=${coachId}`
    })
  },
  
  // 打开地图
  openMap: function() {
    const address = this.data.appointment.course.address
    const venue = this.data.appointment.course.venue
    
    // 调用微信地图API
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        wx.chooseLocation({
          latitude: res.latitude,
          longitude: res.longitude,
          keyword: venue,
          success: (location) => {
            console.log('选择的位置：', location)
          }
        })
      },
      fail: () => {
        wx.showToast({
          title: '获取位置信息失败',
          icon: 'none'
        })
      }
    })
  },
  
  // 拨打电话
  callCoach: function() {
    const phone = this.data.appointment.coach.phone
    wx.makePhoneCall({
      phoneNumber: phone
    })
  },
  
  // 显示取消预约弹窗
  showCancelDialog: function() {
    this.setData({ showCancelDialog: true })
  },
  
  // 隐藏取消预约弹窗
  hideCancelDialog: function() {
    this.setData({ showCancelDialog: false })
  },
  
  // 选择取消原因
  selectReason: function(e) {
    const reason = e.currentTarget.dataset.reason
    this.setData({ selectedReason: reason })
  },
  
  // 取消预约
  cancelAppointment: function() {
    if (!this.data.selectedReason) {
      wx.showToast({
        title: '请选择取消原因',
        icon: 'none'
      })
      return
    }
    
    this.setData({ cancelling: true })
    
    // 模拟API请求
    setTimeout(() => {
      this.setData({
        cancelling: false,
        showCancelDialog: false,
        'appointment.status': 'cancelled',
        'appointment.canCancel': false
      })
      
      wx.showToast({
        title: '已取消预约',
        icon: 'success'
      })
    }, 1500)
    
    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/appointment/cancel`,
      method: 'POST',
      data: {
        id: this.data.id,
        reason: this.data.selectedReason
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          this.setData({
            cancelling: false,
            showCancelDialog: false,
            'appointment.status': 'cancelled',
            'appointment.canCancel': false
          })
          
          wx.showToast({
            title: '已取消预约',
            icon: 'success'
          })
        }
      }
    })
    */
  },
  
  // 去评价
  goToReview: function() {
    wx.navigateTo({
      url: `/pages/appointment/review?id=${this.data.id}`
    })
  },
  
  // 预览图片
  previewImage: function(e) {
    const { urls, current } = e.currentTarget.dataset
    wx.previewImage({
      urls,
      current
    })
  },
  
  // 分享
  onShareAppMessage: function () {
    return {
      title: `我预约了${this.data.appointment.coach.name}的台球课程`,
      path: `/pages/appointment/detail?id=${this.data.id}`,
      imageUrl: '/images/share-appointment.png'
    }
  }
})