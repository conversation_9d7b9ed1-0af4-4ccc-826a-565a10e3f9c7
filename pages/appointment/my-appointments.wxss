/* pages/appointment/my-appointments.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 頁面標題 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 篩選標籤 */
.filter-tabs {
  display: flex;
  background: white;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 10rpx;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s ease;
}

.filter-tab.active {
  color: #667eea;
  font-weight: bold;
  border-bottom-color: #667eea;
}

/* 預約列表 */
.appointment-list {
  padding: 0 20rpx;
}

/* 載入狀態 */
.loading-container {
  text-align: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空狀態 */
.empty-container {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
}

.empty-tips {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 40rpx;
}

.go-booking-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 50rpx;
  font-size: 28rpx;
}

/* 預約項目 */
.appointment-item {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  position: relative;
  overflow: hidden;
}

.appointment-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 8rpx;
  height: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
}

/* 狀態標籤 */
.status-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: white;
  font-weight: bold;
}

.status-pending {
  background: #ff9500;
}

.status-confirmed {
  background: #34c759;
}

.status-completed {
  background: #007aff;
}

.status-cancelled {
  background: #ff3b30;
}

/* 課程信息 */
.course-info {
  margin-bottom: 30rpx;
}

.course-header {
  display: flex;
  align-items: center;
}

.course-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  margin-right: 20rpx;
  background: #f0f0f0;
}

.course-details {
  flex: 1;
}

.course-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.course-type {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
}

.course-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff6b35;
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
}

.price-unit {
  font-size: 22rpx;
  color: #999;
  margin-left: 5rpx;
}

/* 教練信息 */
.coach-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.coach-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background: #e0e0e0;
}

.coach-details {
  flex: 1;
}

.coach-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.coach-title {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.coach-rating {
  display: flex;
  align-items: center;
}

.rating-score {
  font-size: 24rpx;
  color: #ff6b35;
  font-weight: bold;
  margin-right: 5rpx;
}

.rating-star {
  font-size: 24rpx;
  color: #ff6b35;
}

/* 預約時間 */
.appointment-time {
  margin-bottom: 25rpx;
}

.time-row {
  display: flex;
  margin-bottom: 10rpx;
}

.time-label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
}

.time-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 地點信息 */
.location-info {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  padding: 15rpx;
  background: #f0f8ff;
  border-radius: 8rpx;
}

.location-icon {
  font-size: 24rpx;
  margin-right: 10rpx;
}

.location-text {
  font-size: 26rpx;
  color: #333;
}

/* 操作按鈕 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 25rpx;
}

.btn {
  flex: 1;
  border: none;
  border-radius: 25rpx;
  padding: 18rpx 0;
  font-size: 26rpx;
  text-align: center;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.btn-secondary {
  background: white;
  color: #667eea;
  border: 2rpx solid #667eea;
}

.btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 備註信息 */
.appointment-notes {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #fff9e6;
  border-radius: 8rpx;
  border-left: 4rpx solid #ffc107;
}

.notes-label {
  font-size: 24rpx;
  color: #856404;
  font-weight: bold;
}

.notes-content {
  font-size: 24rpx;
  color: #856404;
  margin-left: 10rpx;
}

/* 底部安全區域 */
.safe-bottom {
  height: env(safe-area-inset-bottom);
} 