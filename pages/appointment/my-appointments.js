const app = getApp()

Page({
  data: {
    currentTab: 'all',
    loading: false,
    appointments: [
      // 初始測試數據
      {
        id: 1,
        status: 'confirmed',
        courseTitle: '台球基礎技巧課程',
        courseType: '一對一教學',
        courseImage: '/images/course1.png',
        price: 300,
        unit: '小時',
        coachName: '張教練',
        coachTitle: '國家一級台球教練',
        coachAvatar: '/images/coach1.png',
        coachRating: 4.9,
        appointmentDate: '2024-06-15',
        appointmentTime: '14:00-16:00',
        duration: 2,
        location: '台球館A廳',
        notes: '請提前10分鐘到達',
        createTime: '2024-06-10 10:30:00'
      },
      {
        id: 2,
        status: 'pending',
        courseTitle: '斯諾克進階課程',
        courseType: '小班教學',
        courseImage: '/images/course2.png',
        price: 450,
        unit: '小時',
        coachName: '李教練',
        coachTitle: '職業台球運動員',
        coachAvatar: '/images/coach2.png',
        coachRating: 4.8,
        appointmentDate: '2024-06-20',
        appointmentTime: '19:00-21:00',
        duration: 2,
        location: '台球館B廳',
        notes: '需要自備球桿',
        createTime: '2024-06-12 15:20:00'
      },
      {
        id: 3,
        status: 'completed',
        courseTitle: '九球技巧專項訓練',
        courseType: '一對一教學',
        courseImage: '/images/course3.png',
        price: 350,
        unit: '小時',
        coachName: '王教練',
        coachTitle: '資深台球教練',
        coachAvatar: '/images/coach3.png',
        coachRating: 4.7,
        appointmentDate: '2024-06-05',
        appointmentTime: '10:00-12:00',
        duration: 2,
        location: '台球館C廳',
        notes: '已完成課程',
        createTime: '2024-06-01 09:15:00'
      },
      {
        id: 4,
        status: 'cancelled',
        courseTitle: '台球入門課程',
        courseType: '體驗課',
        courseImage: '/images/course4.png',
        price: 200,
        unit: '小時',
        coachName: '趙教練',
        coachTitle: '青少年台球教練',
        coachAvatar: '/images/coach4.png',
        coachRating: 4.6,
        appointmentDate: '2024-06-08',
        appointmentTime: '16:00-17:00',
        duration: 1,
        location: '台球館D廳',
        notes: '因時間衝突取消',
        createTime: '2024-06-03 14:45:00'
      }
    ],
    filteredAppointments: [],
    
    // 標籤名稱映射
    tabNames: {
      all: '全部',
      pending: '待確認',
      confirmed: '已確認',
      completed: '已完成',
      cancelled: '已取消'
    },
    
    // 狀態名稱映射
    statusNames: {
      pending: '待確認',
      confirmed: '已確認',
      completed: '已完成',
      cancelled: '已取消'
    }
  },

  onLoad() {
    console.log('我的預約頁面加載')
    this.filterAppointments()
  },

  onShow() {
    // 頁面顯示時刷新數據
    this.loadAppointments()
  },

  // 載入預約數據
  loadAppointments() {
    this.setData({ loading: true })
    
    // 模擬API請求
    setTimeout(() => {
      // 這裡可以添加實際的API請求邏輯
      this.filterAppointments()
      this.setData({ loading: false })
    }, 500)
  },

  // 切換標籤
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({ currentTab: tab })
    this.filterAppointments()
  },

  // 篩選預約數據
  filterAppointments() {
    const { appointments, currentTab } = this.data
    let filtered = appointments

    if (currentTab !== 'all') {
      filtered = appointments.filter(item => item.status === currentTab)
    }

    // 按創建時間倒序排列
    filtered.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))

    this.setData({ filteredAppointments: filtered })
  },

  // 跳轉到預約詳情
  goToAppointmentDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/appointment/detail?id=${id}`
    })
  },

  // 取消預約
  cancelAppointment(e) {
    const id = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '確認取消',
      content: '確定要取消這個預約嗎？',
      confirmText: '確認取消',
      cancelText: '再想想',
      success: (res) => {
        if (res.confirm) {
          this.updateAppointmentStatus(id, 'cancelled')
          wx.showToast({
            title: '預約已取消',
            icon: 'success'
          })
        }
      }
    })
  },

  // 聯繫教練
  contactCoach(e) {
    const id = e.currentTarget.dataset.id
    const appointment = this.data.appointments.find(item => item.id == id)
    
    if (appointment) {
      wx.showModal({
        title: '聯繫教練',
        content: `即將聯繫${appointment.coachName}教練`,
        confirmText: '撥打電話',
        cancelText: '發送消息',
        success: (res) => {
          if (res.confirm) {
            // 撥打電話邏輯
            wx.makePhoneCall({
              phoneNumber: '13800138000' // 這裡應該是教練的實際電話
            })
          } else {
            // 發送消息邏輯
            wx.showToast({
              title: '消息功能開發中',
              icon: 'none'
            })
          }
        }
      })
    }
  },

  // 改期預約
  reschedule(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/appointment/reschedule?id=${id}`
    })
  },

  // 查看證書
  viewCertificate(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/certificate/view?appointmentId=${id}`
    })
  },

  // 評價課程
  rateAppointment(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/appointment/rate?id=${id}`
    })
  },

  // 重新預約
  rebookAppointment(e) {
    const id = e.currentTarget.dataset.id
    const appointment = this.data.appointments.find(item => item.id == id)
    
    if (appointment) {
      wx.navigateTo({
        url: `/pages/coach/detail?id=${appointment.coachId || 1}&rebookCourse=${appointment.courseTitle}`
      })
    }
  },

  // 跳轉到教練列表
  goToCoachList() {
    wx.switchTab({
      url: '/pages/coach/list'
    })
  },

  // 更新預約狀態
  updateAppointmentStatus(id, status) {
    const appointments = this.data.appointments.map(item => {
      if (item.id == id) {
        return { ...item, status }
      }
      return item
    })
    
    this.setData({ appointments })
    this.filterAppointments()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadAppointments()
    wx.stopPullDownRefresh()
  }
}) 