// pages/appointment/review.js
const app = getApp()

Page({
  data: {
    id: null, // 预约ID
    loading: true, // 加载状态
    submitting: false, // 提交状态
    
    // 预约信息
    appointment: null,
    
    // 评分
    rating: 5,
    ratingLabels: ['很差', '一般', '还行', '不错', '很棒'],
    
    // 评价内容
    content: '',
    minContentLength: 10,
    maxContentLength: 500,
    
    // 图片上传
    images: [],
    maxImageCount: 9,
    
    // 表单验证
    formValid: false
  },

  onLoad: function (options) {
    this.setData({
      id: options.id
    })
    
    this.loadAppointmentDetail()
  },
  
  // 加载预约详情
  loadAppointmentDetail: function() {
    this.setData({ loading: true })
    
    // 模拟API请求
    setTimeout(() => {
      // 模拟数据
      const appointment = {
        id: this.data.id,
        coach: {
          id: 123,
          name: '张教练',
          avatar: '/images/default-avatar.png',
          level: 'senior'
        },
        course: {
          type: '技术提升',
          date: '2024-02-20',
          time: '15:00-17:00',
          venue: '星牌台球俱乐部'
        }
      }
      
      this.setData({
        loading: false,
        appointment
      })
    }, 1000)
    
    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/appointment/detail`,
      method: 'GET',
      data: { id: this.data.id },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          this.setData({
            loading: false,
            appointment: res.data.data
          })
        }
      }
    })
    */
  },
  
  // 更改评分
  onRatingChange: function(e) {
    const rating = e.currentTarget.dataset.rating
    this.setData({ 
      rating,
      formValid: this.checkFormValid()
    })
  },
  
  // 更新评价内容
  onContentInput: function(e) {
    const content = e.detail.value
    this.setData({ 
      content,
      formValid: this.checkFormValid()
    })
  },
  
  // 选择图片
  chooseImage: function() {
    const { images, maxImageCount } = this.data
    const remaining = maxImageCount - images.length
    
    if (remaining <= 0) {
      wx.showToast({
        title: `最多上传${maxImageCount}张图片`,
        icon: 'none'
      })
      return
    }
    
    wx.chooseImage({
      count: remaining,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 模拟上传图片
        wx.showLoading({
          title: '上传中...',
          mask: true
        })
        
        setTimeout(() => {
          const newImages = [...this.data.images, ...res.tempFilePaths]
          this.setData({
            images: newImages,
            formValid: this.checkFormValid()
          })
          
          wx.hideLoading()
        }, 1500)
        
        // 实际上传图片示例
        /*
        const uploads = res.tempFilePaths.map(path => {
          return new Promise((resolve, reject) => {
            wx.uploadFile({
              url: `${app.globalData.baseUrl}/api/upload`,
              filePath: path,
              name: 'file',
              success: (res) => {
                const data = JSON.parse(res.data)
                if (data.success) {
                  resolve(data.url)
                } else {
                  reject(new Error('上传失败'))
                }
              },
              fail: reject
            })
          })
        })
        
        Promise.all(uploads).then(urls => {
          const newImages = [...this.data.images, ...urls]
          this.setData({
            images: newImages,
            formValid: this.checkFormValid()
          })
        }).catch(err => {
          wx.showToast({
            title: '图片上传失败',
            icon: 'none'
          })
        }).finally(() => {
          wx.hideLoading()
        })
        */
      }
    })
  },
  
  // 预览图片
  previewImage: function(e) {
    const { url } = e.currentTarget.dataset
    wx.previewImage({
      urls: this.data.images,
      current: url
    })
  },
  
  // 删除图片
  deleteImage: function(e) {
    const { index } = e.currentTarget.dataset
    const images = [...this.data.images]
    images.splice(index, 1)
    
    this.setData({
      images,
      formValid: this.checkFormValid()
    })
  },
  
  // 检查表单是否有效
  checkFormValid: function() {
    const { content, minContentLength } = this.data
    return content.length >= minContentLength
  },
  
  // 提交评价
  submitReview: function() {
    if (!this.data.formValid) {
      wx.showToast({
        title: `请至少输入${this.data.minContentLength}个字的评价`,
        icon: 'none'
      })
      return
    }
    
    this.setData({ submitting: true })
    
    const data = {
      appointmentId: this.data.id,
      rating: this.data.rating,
      content: this.data.content,
      images: this.data.images
    }
    
    // 模拟提交
    setTimeout(() => {
      this.setData({ submitting: false })
      
      wx.showToast({
        title: '评价成功',
        icon: 'success'
      })
      
      // 返回上一页并刷新
      setTimeout(() => {
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2]
        if (prevPage) {
          prevPage.loadAppointmentDetail()
        }
        wx.navigateBack()
      }, 1500)
    }, 2000)
    
    // 实际提交示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/appointment/review`,
      method: 'POST',
      data,
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          wx.showToast({
            title: '评价成功',
            icon: 'success'
          })
          
          // 返回上一页并刷新
          setTimeout(() => {
            const pages = getCurrentPages()
            const prevPage = pages[pages.length - 2]
            if (prevPage) {
              prevPage.loadAppointmentDetail()
            }
            wx.navigateBack()
          }, 1500)
        }
      },
      fail: () => {
        wx.showToast({
          title: '评价失败，请重试',
          icon: 'none'
        })
      },
      complete: () => {
        this.setData({ submitting: false })
      }
    })
    */
  }
})