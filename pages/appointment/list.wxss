/* pages/appointment/list.wxss */

.container {
  min-height: 100vh;
  background-color: #f7fafc;
}

/* 搜索栏样式 */
.search-section {
  background: linear-gradient(135deg, #5a9178 0%, #4a7c64 100%);
  padding: 24rpx 32rpx 32rpx;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-wrapper {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.search-icon {
  font-size: 32rpx;
  color: #718096;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #2d3748;
  height: 60rpx;
  line-height: 60rpx;
}

.search-btn {
  color: #5a9178;
  font-size: 28rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background-color: rgba(90, 145, 120, 0.1);
}

.search-btn:active {
  transform: scale(0.95);
}

/* 筛选面板样式 */
.filter-panel {
  background-color: #ffffff;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  min-width: 120rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  flex: 1;
  margin-left: 24rpx;
}

.filter-option {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: #edf2f7;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #4a5568;
  border: 2rpx solid transparent;
}

.filter-option.active {
  background-color: rgba(90, 145, 120, 0.1);
  color: #5a9178;
  border-color: #5a9178;
}

.filter-option:active {
  transform: scale(0.95);
}

/* 教练瀑布流样式 */
.coach-waterfall {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  min-height: 200rpx;
  gap: 20rpx;
}

.coach-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.coach-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
  text-align: center;
  padding: 40rpx 30rpx 30rpx;
}

.coach-card:active {
  transform: scale(0.98);
}

.coach-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  margin: 0 auto 24rpx;
  background-color: #f5f5f5;
  border: 4rpx solid #e2e8f0;
  display: block;
}

.coach-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.coach-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12rpx;
}

.coach-level {
  display: inline-block;
  background-color: #f0f8e8;
  color: #5a9178;
  font-size: 24rpx;
  font-weight: 500;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.coach-venue {
  font-size: 26rpx;
  color: #718096;
  line-height: 1.5;
  margin-bottom: 16rpx;
  text-align: center;
}

.venue-location {
  font-size: 26rpx;
  color: #5a9178;
  text-decoration: underline;
  cursor: pointer;
}

.venue-location:active {
  opacity: 0.7;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #718096;
  font-size: 26rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e2e8f0;
  border-top: 4rpx solid #5a9178;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #718096;
  margin-bottom: 40rpx;
}

/* 底部安全区域 */
.safe-bottom {
  height: env(safe-area-inset-bottom);
  background-color: #f7fafc;
}