<!--pages/appointment/list.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-wrapper">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索教练、球馆" 
        value="{{searchText}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <text class="search-btn" bindtap="onSearch" wx:if="{{searchText}}">搜索</text>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel">
    <view class="filter-row">
      <text class="filter-label">等级</text>
      <view class="filter-options">
        <view 
          class="filter-option {{item.active ? 'active' : ''}}"
          wx:for="{{levelFilters}}" 
          wx:key="id"
          bindtap="toggleLevelFilter"
          data-id="{{item.id}}"
        >
          {{item.name}}
        </view>
      </view>
    </view>
    
    <view class="filter-row">
      <text class="filter-label">区域</text>
      <view class="filter-options">
        <view 
          class="filter-option {{item.active ? 'active' : ''}}"
          wx:for="{{areaFilters}}" 
          wx:key="id"
          bindtap="toggleAreaFilter"
          data-id="{{item.id}}"
        >
          {{item.name}}
        </view>
      </view>
    </view>
  </view>

  <!-- 教练瀑布流 -->
  <view class="coach-waterfall">
    <view class="coach-column">
      <view 
        class="coach-card"
        wx:for="{{leftColumn}}" 
        wx:key="id"
        bindtap="viewCoachDetail"
        data-id="{{item.id}}"
      >
        <image class="coach-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
        <view class="coach-info">
          <view class="coach-name">{{item.name}}</view>
          <view class="coach-level">{{item.level}}</view>
          <view class="coach-venue">常驻球馆：{{item.venue}}</view>
          <view class="venue-location" bindtap="viewVenueLocation" data-venue="{{item.venue}}" catchtap="viewVenueLocation">
            查看球房位置
          </view>
        </view>
      </view>
    </view>
    
    <view class="coach-column">
      <view 
        class="coach-card"
        wx:for="{{rightColumn}}" 
        wx:key="id"
        bindtap="viewCoachDetail"
        data-id="{{item.id}}"
      >
        <image class="coach-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
        <view class="coach-info">
          <view class="coach-name">{{item.name}}</view>
          <view class="coach-level">{{item.level}}</view>
          <view class="coach-venue">常驻球馆：{{item.venue}}</view>
          <view class="venue-location" bindtap="viewVenueLocation" data-venue="{{item.venue}}" catchtap="viewVenueLocation">
            查看球房位置
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && coachList.length === 0}}">
    <image class="empty-image" src="/images/empty-coach.png" mode="aspectFit"></image>
    <view class="empty-text">暂无教练信息</view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-bottom"></view>
</view>