/* pages/appointment/detail.wxss */

.container {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding-bottom: 120rpx;
}

/* 加载中 */
.loading-container {
  padding: var(--spacing-2xl) 0;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  margin: 0 auto var(--spacing-md);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: var(--text-light);
}

/* 预约状态卡片 */
.status-card {
  margin: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.status-card.pending {
  background: linear-gradient(135deg, var(--warning-color), #ff9800);
}

.status-card.completed {
  background: linear-gradient(135deg, var(--success-color), #2e7d32);
}

.status-card.cancelled {
  background: linear-gradient(135deg, var(--text-muted), #616161);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  color: white;
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
}

.order-no {
  font-size: 24rpx;
  opacity: 0.8;
}

.status-content {
  background-color: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.countdown {
  text-align: center;
  color: white;
}

.countdown-title {
  font-size: 28rpx;
  margin-bottom: var(--spacing-md);
}

.countdown-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.time-item {
  text-align: center;
}

.time-value {
  width: 80rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  font-size: 40rpx;
  font-weight: 600;
}

.time-label {
  font-size: 24rpx;
  margin-top: var(--spacing-xs);
}

.time-separator {
  font-size: 40rpx;
  font-weight: 600;
  margin: 0 var(--spacing-sm);
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: var(--spacing-md);
}

.status-icon image {
  width: 100%;
  height: 100%;
}

.status-message {
  font-size: 28rpx;
  color: white;
}

/* 信息卡片 */
.info-card {
  margin: var(--spacing-md);
  padding: var(--spacing-lg);
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color);
}

.card-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  position: relative;
  padding-left: var(--spacing-md);
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 30rpx;
  background-color: var(--primary-color);
  border-radius: 3rpx;
}

/* 教练信息 */
.coach-info {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) 0;
}

.coach-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border: 3rpx solid var(--border-color);
}

.coach-detail {
  flex: 1;
}

.coach-name {
  font-size: 32rpx;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
}

.coach-level {
  font-size: 26rpx;
  color: var(--warning-color);
  background-color: rgba(255, 212, 59, 0.1);
  padding: 4rpx var(--spacing-sm);
  border-radius: var(--border-radius);
  display: inline-block;
  border: 1rpx solid var(--warning-color);
}

.coach-arrow {
  padding: var(--spacing-sm);
  color: var(--text-light);
}

.coach-arrow image {
  width: 32rpx;
  height: 32rpx;
}

/* 信息列表 */
.info-list {
  
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.info-value.price {
  color: #ff4d4f;
  font-weight: bold;
}

.info-item.location {
  align-items: flex-start;
}

.location-value {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.location-text {
  flex: 1;
}

.address {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.location-icon {
  padding: 10rpx;
}

.location-icon image {
  width: 32rpx;
  height: 32rpx;
}

/* 评价信息 */
.review-content {
  padding: 20rpx 0;
}

.review-rating {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.rating-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.rating-stars {
  display: flex;
  margin-right: 10rpx;
}

.rating-stars image {
  width: 30rpx;
  height: 30rpx;
  margin-right: 6rpx;
}

.rating-score {
  font-size: 28rpx;
  color: #ff9500;
}

.review-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.review-images {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.review-images image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin: 0 20rpx 20rpx 0;
}

.review-time {
  font-size: 24rpx;
  color: #999;
  text-align: right;
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0 10rpx;
}

.action-btn image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.action-btn.call {
  background-color: #fff;
  color: #77A88D;
  border: 1rpx solid #77A88D;
}

.action-btn.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.action-btn.review {
  background-color: #77A88D;
  color: #fff;
}

.action-btn.share {
  background-color: #ff9500;
  color: #fff;
}

/* 取消预约弹窗 */
.cancel-dialog {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.cancel-dialog.show {
  transform: translateY(0);
}

.dialog-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.dialog-close {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  padding: 10rpx;
}

.dialog-close image {
  width: 32rpx;
  height: 32rpx;
}

.dialog-body {
  padding: 30rpx;
}

.dialog-tips {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.reason-list {
  
}

.reason-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.reason-item:last-child {
  border-bottom: none;
}

.reason-text {
  font-size: 28rpx;
  color: #333;
}

.reason-check {
  padding: 10rpx;
}

.reason-check image {
  width: 32rpx;
  height: 32rpx;
}

.reason-item.active .reason-text {
  color: #77A88D;
}

.dialog-warning {
  display: flex;
  align-items: flex-start;
  background-color: #fff9f0;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 30rpx;
}

.warning-icon {
  width: 36rpx;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  background-color: #ff9500;
  color: #fff;
  border-radius: 50%;
  font-weight: bold;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
  font-size: 24rpx;
  color: #ff9500;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f5f5f5;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 40rpx;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.confirm-btn {
  background-color: #ff4d4f;
  color: #fff;
}

/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mask.show {
  opacity: 1;
  visibility: visible;
}