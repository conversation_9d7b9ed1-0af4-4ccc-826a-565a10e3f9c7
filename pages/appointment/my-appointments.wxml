<!--pages/appointment/my-appointments.wxml-->
<view class="container">
  <!-- 頁面標題 -->
  <view class="page-header">
    <text class="page-title">我的預約</text>
    <text class="page-subtitle">管理您的課程預約</text>
  </view>

  <!-- 篩選標籤 -->
  <view class="filter-tabs">
    <view 
      class="filter-tab {{currentTab === 'all' ? 'active' : ''}}" 
      bindtap="switchTab" 
      data-tab="all"
    >
      全部
    </view>
    <view 
      class="filter-tab {{currentTab === 'pending' ? 'active' : ''}}" 
      bindtap="switchTab" 
      data-tab="pending"
    >
      待確認
    </view>
    <view 
      class="filter-tab {{currentTab === 'confirmed' ? 'active' : ''}}" 
      bindtap="switchTab" 
      data-tab="confirmed"
    >
      已確認
    </view>
    <view 
      class="filter-tab {{currentTab === 'completed' ? 'active' : ''}}" 
      bindtap="switchTab" 
      data-tab="completed"
    >
      已完成
    </view>
    <view 
      class="filter-tab {{currentTab === 'cancelled' ? 'active' : ''}}" 
      bindtap="switchTab" 
      data-tab="cancelled"
    >
      已取消
    </view>
  </view>

  <!-- 預約列表 -->
  <view class="appointment-list">
    <block wx:if="{{loading}}">
      <view class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加載中...</text>
      </view>
    </block>
    
    <block wx:elif="{{filteredAppointments.length === 0}}">
      <view class="empty-container">
        <image class="empty-icon" src="/images/empty-appointment.png"></image>
        <text class="empty-text">暫無{{tabNames[currentTab]}}預約</text>
        <text class="empty-tips">快去預約您喜歡的教練課程吧</text>
        <button class="go-booking-btn" bindtap="goToCoachList">去預約</button>
      </view>
    </block>
    
    <block wx:else>
      <view 
        class="appointment-item" 
        wx:for="{{filteredAppointments}}" 
        wx:key="id"
        bindtap="goToAppointmentDetail"
        data-id="{{item.id}}"
      >
        <!-- 狀態標籤 -->
        <view class="status-badge status-{{item.status}}">
          {{statusNames[item.status]}}
        </view>

        <!-- 課程信息 -->
        <view class="course-info">
          <view class="course-header">
            <image class="course-image" src="{{item.courseImage}}" mode="aspectFill"></image>
            <view class="course-details">
              <text class="course-title">{{item.courseTitle}}</text>
              <text class="course-type">{{item.courseType}}</text>
              <view class="course-price">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{item.price}}</text>
                <text class="price-unit">/{{item.unit}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 教練信息 -->
        <view class="coach-info">
          <image class="coach-avatar" src="{{item.coachAvatar}}" mode="aspectFill"></image>
          <view class="coach-details">
            <text class="coach-name">{{item.coachName}}</text>
            <text class="coach-title">{{item.coachTitle}}</text>
            <view class="coach-rating">
              <text class="rating-score">{{item.coachRating}}</text>
              <text class="rating-star">★</text>
            </view>
          </view>
        </view>

        <!-- 預約時間 -->
        <view class="appointment-time">
          <view class="time-row">
            <text class="time-label">預約時間：</text>
            <text class="time-value">{{item.appointmentDate}} {{item.appointmentTime}}</text>
          </view>
          <view class="time-row">
            <text class="time-label">課程時長：</text>
            <text class="time-value">{{item.duration}}小時</text>
          </view>
        </view>

        <!-- 地點信息 -->
        <view class="location-info" wx:if="{{item.location}}">
          <text class="location-icon">📍</text>
          <text class="location-text">{{item.location}}</text>
        </view>

        <!-- 操作按鈕 -->
        <view class="action-buttons">
          <block wx:if="{{item.status === 'pending'}}">
            <button class="btn btn-secondary" catchtap="cancelAppointment" data-id="{{item.id}}">取消預約</button>
            <button class="btn btn-primary" catchtap="contactCoach" data-id="{{item.id}}">聯繫教練</button>
          </block>
          
          <block wx:elif="{{item.status === 'confirmed'}}">
            <button class="btn btn-secondary" catchtap="reschedule" data-id="{{item.id}}">改期</button>
            <button class="btn btn-primary" catchtap="contactCoach" data-id="{{item.id}}">聯繫教練</button>
          </block>
          
          <block wx:elif="{{item.status === 'completed'}}">
            <button class="btn btn-secondary" catchtap="viewCertificate" data-id="{{item.id}}">查看證書</button>
            <button class="btn btn-primary" catchtap="rateAppointment" data-id="{{item.id}}">評價課程</button>
          </block>
          
          <block wx:elif="{{item.status === 'cancelled'}}">
            <button class="btn btn-primary" catchtap="rebookAppointment" data-id="{{item.id}}">重新預約</button>
          </block>
        </view>

        <!-- 備註信息 -->
        <view class="appointment-notes" wx:if="{{item.notes}}">
          <text class="notes-label">備註：</text>
          <text class="notes-content">{{item.notes}}</text>
        </view>
      </view>
    </block>
  </view>

  <!-- 底部安全區域 -->
  <view class="safe-bottom"></view>
</view> 