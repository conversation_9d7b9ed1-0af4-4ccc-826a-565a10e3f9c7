<!--pages/appointment/detail.wxml-->
<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <block wx:else>
    <!-- 预约状态 -->
    <view class="status-card {{appointment.status}}">
      <view class="status-header">
        <view class="status-text">{{statusText[appointment.status]}}</view>
        <view class="order-no">订单编号：{{appointment.orderNo}}</view>
      </view>
      
      <block wx:if="{{appointment.status === 'pending'}}">
        <view class="status-content">
          <view class="countdown">
            <view class="countdown-title">距离上课还有</view>
            <view class="countdown-time">
              <view class="time-item">
                <view class="time-value">{{countdown.days}}</view>
                <view class="time-label">天</view>
              </view>
              <view class="time-separator">:</view>
              <view class="time-item">
                <view class="time-value">{{countdown.hours}}</view>
                <view class="time-label">时</view>
              </view>
              <view class="time-separator">:</view>
              <view class="time-item">
                <view class="time-value">{{countdown.minutes}}</view>
                <view class="time-label">分</view>
              </view>
            </view>
          </view>
        </view>
      </block>
      
      <block wx:elif="{{appointment.status === 'completed'}}">
        <view class="status-content">
          <view class="status-icon">
            <image src="/images/icon-completed.png"></image>
          </view>
          <view class="status-message">课程已完成，感谢您的参与</view>
        </view>
      </block>
      
      <block wx:elif="{{appointment.status === 'cancelled'}}">
        <view class="status-content">
          <view class="status-icon">
            <image src="/images/icon-cancelled.png"></image>
          </view>
          <view class="status-message">预约已取消</view>
        </view>
      </block>
    </view>
    
    <!-- 教练信息 -->
    <view class="info-card">
      <view class="card-title">教练信息</view>
      <view class="coach-info" bindtap="goToCoachDetail">
        <image class="coach-avatar" src="{{appointment.coach.avatar}}" mode="aspectFill"></image>
        <view class="coach-detail">
          <view class="coach-name">{{appointment.coach.name}}</view>
          <view class="coach-level">
            {{appointment.coach.level === 'national' ? '国家级教练' : 
              appointment.coach.level === 'senior' ? '高级教练' : 
              appointment.coach.level === 'intermediate' ? '中级教练' : '初级教练'}}
          </view>
        </view>
        <view class="coach-arrow">
          <image src="/images/icon-arrow.png"></image>
        </view>
      </view>
    </view>
    
    <!-- 课程信息 -->
    <view class="info-card">
      <view class="card-title">课程信息</view>
      <view class="info-list">
        <view class="info-item">
          <view class="info-label">课程类型</view>
          <view class="info-value">{{appointment.course.type}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">上课时间</view>
          <view class="info-value">{{appointment.course.date}} {{appointment.course.time}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">课程时长</view>
          <view class="info-value">{{appointment.course.duration}}小时</view>
        </view>
        <view class="info-item location" bindtap="openMap">
          <view class="info-label">上课地点</view>
          <view class="info-value location-value">
            <view class="location-text">
              <view>{{appointment.course.venue}}</view>
              <view class="address">{{appointment.course.address}}</view>
            </view>
            <view class="location-icon">
              <image src="/images/icon-location.png"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 支付信息 -->
    <view class="info-card">
      <view class="card-title">支付信息</view>
      <view class="info-list">
        <view class="info-item">
          <view class="info-label">支付金额</view>
          <view class="info-value price">¥{{appointment.payment.amount}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">支付方式</view>
          <view class="info-value">{{appointment.payment.method}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">支付时间</view>
          <view class="info-value">{{appointment.payment.time}}</view>
        </view>
      </view>
    </view>
    
    <!-- 评价信息 -->
    <view class="info-card" wx:if="{{appointment.hasReviewed}}">
      <view class="card-title">我的评价</view>
      <view class="review-content">
        <view class="review-rating">
          <view class="rating-label">评分：</view>
          <view class="rating-stars">
            <block wx:for="{{5}}" wx:key="index">
              <text class="star-text">{{index + 1 <= Math.floor(appointment.coach.rating) ? '★' : index + 0.5 <= appointment.coach.rating ? '★' : '☆'}}</text>
            </block>
          </view>
          <view class="rating-score">{{appointment.review.rating}}</view>
        </view>
        
        <view class="review-text">{{appointment.review.content}}</view>
        
        <view class="review-images" wx:if="{{appointment.review.images.length > 0}}">
          <image 
            wx:for="{{appointment.review.images}}" 
            wx:key="index"
            src="{{item}}"
            mode="aspectFill"
            bindtap="previewImage"
            data-urls="{{appointment.review.images}}"
            data-current="{{item}}"
          ></image>
        </view>
        
        <view class="review-time">{{appointment.review.createTime}}</view>
      </view>
    </view>
    
    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <block wx:if="{{appointment.status === 'pending'}}">
        <button class="action-btn call" bindtap="callCoach">
          <image src="/images/icon-call.png"></image>
          <text>联系教练</text>
        </button>
        <button 
          class="action-btn cancel" 
          bindtap="showCancelDialog"
          wx:if="{{appointment.canCancel}}"
        >
          <image src="/images/icon-cancel.png"></image>
          <text>取消预约</text>
        </button>
      </block>
      
      <block wx:elif="{{appointment.status === 'completed'}}">
        <button 
          class="action-btn review" 
          bindtap="goToReview"
          wx:if="{{appointment.canReview && !appointment.hasReviewed}}"
        >
          <image src="/images/icon-review.png"></image>
          <text>评价课程</text>
        </button>
        <button class="action-btn share" open-type="share">
          <image src="/images/icon-share.png"></image>
          <text>分享</text>
        </button>
      </block>
    </view>
    
    <!-- 取消预约弹窗 -->
    <view class="cancel-dialog {{showCancelDialog ? 'show' : ''}}">
      <view class="dialog-content">
        <view class="dialog-header">
          <view class="dialog-title">取消预约</view>
          <view class="dialog-close" bindtap="hideCancelDialog">
            <image src="/images/icon-close.png"></image>
          </view>
        </view>
        
        <view class="dialog-body">
          <view class="dialog-tips">请选择取消原因：</view>
          <view class="reason-list">
            <view 
              class="reason-item {{selectedReason === item ? 'active' : ''}}"
              wx:for="{{cancelReasons}}"
              wx:key="index"
              bindtap="selectReason"
              data-reason="{{item}}"
            >
              <view class="reason-text">{{item}}</view>
              <view class="reason-check">
                <image src="{{selectedReason === item ? '/images/icon-check-active.png' : '/images/icon-check.png'}}"></image>
              </view>
            </view>
          </view>
          
          <view class="dialog-warning">
            <view class="warning-icon">!</view>
            <view class="warning-text">取消预约后，订单金额将按照退款规则返还到您的支付账户</view>
          </view>
        </view>
        
        <view class="dialog-footer">
          <button 
            class="cancel-btn" 
            bindtap="hideCancelDialog"
          >
            再想想
          </button>
          <button 
            class="confirm-btn" 
            bindtap="cancelAppointment"
            loading="{{cancelling}}"
          >
            确认取消
          </button>
        </view>
      </view>
    </view>
    
    <!-- 遮罩层 -->
    <view class="mask {{showCancelDialog ? 'show' : ''}}" bindtap="hideCancelDialog"></view>
  </block>
</view>