/* pages/appointment/review.wxss */

.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

/* 加载中 */
.loading-container {
  padding: 200rpx 0;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3cc51f;
  border-radius: 50%;
  margin: 0 auto 20rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 预约信息 */
.appointment-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.coach-info {
  display: flex;
  align-items: center;
}

.coach-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.coach-detail {
  flex: 1;
}

.coach-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.course-info {
  font-size: 26rpx;
  color: #666;
}

/* 评价表单 */
.review-form {
  background-color: #fff;
  padding: 0 30rpx;
}

.form-section {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

/* 评分 */
.rating-container {
  display: flex;
  align-items: center;
}

.rating-stars {
  display: flex;
  margin-right: 20rpx;
}

.star-item {
  padding: 10rpx;
  margin-right: 10rpx;
}

.star-item image {
  width: 50rpx;
  height: 50rpx;
}

.rating-label {
  font-size: 28rpx;
  color: #ff9500;
}

/* 评价内容 */
.content-container {
  position: relative;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  background-color: #fafafa;
}

.content-input {
  width: 100%;
  height: 240rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.content-counter {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.content-counter.warning {
  color: #ff4d4f;
}

/* 上传图片 */
.images-container {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin: 0 20rpx 20rpx 0;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn image {
  width: 24rpx;
  height: 24rpx;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-btn image {
  width: 60rpx;
  height: 60rpx;
  opacity: 0.5;
}

/* 提交按钮 */
.submit-container {
  padding: 40rpx 30rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #3cc51f;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
}

.submit-btn.disabled {
  background-color: #ccc;
}

/* 提示 */
.tips-container {
  padding: 0 30rpx;
  text-align: center;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
}