<!--pages/index/index.wxml-->
<view class="container">
  <!-- 轮播图 -->
  <swiper class="banner" indicator-dots="true" autoplay="true" interval="3000" duration="500" circular="true">
    <swiper-item>
      <image class="banner-image" src="/images/banner/banner1.jpg" mode="aspectFill"></image>
    </swiper-item>
    <swiper-item>
      <image class="banner-image" src="/images/banner/banner2.jpg" mode="aspectFill"></image>
    </swiper-item>
    <swiper-item>
      <image class="banner-image" src="/images/banner/banner3.jpg" mode="aspectFill"></image>
    </swiper-item>
  </swiper>

  <!-- 搜索区域 -->
  <view class="search-container">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索教练、商品" 
        value="{{searchKeyword}}"
        bindinput="onInputSearch"
        bindconfirm="onSearch"
        confirm-type="search"
      />
      <text wx:if="{{searchKeyword}}" class="clear-icon" bindtap="onClearSearch">✖</text>
    </view>
  </view>
  
  <!-- CQ商城 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">CQ商城</text>
      <text class="section-more" bindtap="goToCourseList">查看更多 ></text>
    </view>
    
    <!-- 商品瀑布流 -->
    <view class="goods-waterfall">
      <block wx:if="{{courseList.length === 0}}">
        <view class="empty-tip">暂无商品信息</view>
      </block>
      <block wx:else>
        <!-- 左列 -->
        <view class="goods-column">
          <view 
            class="goods-item" 
            wx:for="{{courseList}}" 
            wx:key="id" 
            wx:if="{{index % 2 === 0}}"
            bindtap="goToGoodsDetail" 
            data-id="{{item.id}}"
          >
            <image 
              class="goods-image" 
              src="{{item.image}}" 
              mode="widthFix"
              binderror="onImageError"
              bindload="onImageLoad"
              data-id="{{item.id}}"
              lazy-load="true"
            ></image>
            <view class="goods-info">
              <text class="goods-title">{{item.title}}</text>
              <view class="goods-price-row">
                <view class="goods-price">
                  <text class="price-symbol">¥</text>
                  <text class="price-value">{{item.price}}</text>
                  <text class="price-original">¥{{item.originalPrice}}</text>
                </view>
              </view>
              <view class="goods-bottom">
                <text class="goods-sold">已售 {{item.soldCount}}</text>
                <view class="goods-cart" catchtap="addToCart" data-id="{{item.id}}">
                  <text class="cart-icon">🛒</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 右列 -->
        <view class="goods-column">
          <view 
            class="goods-item" 
            wx:for="{{courseList}}" 
            wx:key="id" 
            wx:if="{{index % 2 === 1}}"
            bindtap="goToGoodsDetail" 
            data-id="{{item.id}}"
          >
            <image 
              class="goods-image" 
              src="{{item.image}}" 
              mode="widthFix"
              binderror="onImageError"
              bindload="onImageLoad"
              data-id="{{item.id}}"
              lazy-load="true"
            ></image>
            <view class="goods-info">
              <text class="goods-title">{{item.title}}</text>
              <view class="goods-price-row">
                <view class="goods-price">
                  <text class="price-symbol">¥</text>
                  <text class="price-value">{{item.price}}</text>
                  <text class="price-original">¥{{item.originalPrice}}</text>
                </view>
              </view>
              <view class="goods-bottom">
                <text class="goods-sold">已售 {{item.soldCount}}</text>
                <view class="goods-cart" catchtap="addToCart" data-id="{{item.id}}">
                  <text class="cart-icon">🛒</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-bottom"></view>
</view>