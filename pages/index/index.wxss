/* pages/index/index.wxss */

/* 页面容器 */
page {
  padding: 0 !important;
  margin: 0 !important;
}

/* 主容器 */
.container {
  padding: 0 !important;
  margin: 0 !important;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 搜索区域 */
.search-container {
  padding: 30rpx 20rpx;
  background-color: #fff;
  margin: 0;
  border-radius: 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 48rpx;
  padding: 16rpx 24rpx;
}

.search-icon, .clear-icon {
  font-size: 28rpx;
  color: #999;
}

.search-input {
  flex: 1;
  color: #333;
  font-size: 28rpx;
  margin: 0 16rpx;
}

.search-input::placeholder {
  color: #999;
}

/* 轮播图 */
.banner {
  width: 100%;
  height: 450rpx;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
  display: block;
  box-sizing: border-box;
}

/* 確保swiper組件本身沒有邊距 */
swiper {
  margin: 0 !important;
  padding: 0 !important;
  width: 100% !important;
  box-sizing: border-box;
}

swiper-item {
  margin: 0 !important;
  padding: 0 !important;
  width: 100% !important;
}

.banner-image {
  width: 100%;
  height: 100%;
  display: block;
  margin: 0 !important;
  padding: 0 !important;
  object-fit: cover;
}

.banner-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #5a9178 0%, #7ab59c 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  margin: 0;
  padding: 0;
}

/* 功能导航 - 已删除相关样式 */

/* 内容区块 */
.section {
  margin: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 10rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 28rpx;
  color: #5a9178;
}

/* 教练列表 - 卡片化 */
.coach-list {
  padding: 0 30rpx; /* 列表左右边距 */
}

.coach-item {
  display: flex;
  align-items: center; /* 垂直居中 */
  padding: 30rpx; /* 增加内边距 */
  background-color: #fff;
  border-radius: 20rpx; /* 圆角 */
  margin-bottom: 20rpx; /* 增加底部间距 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05); /* 更明显的阴影 */
  border-bottom: none; /* 移除原有底部边框 */
}

.coach-item:last-child {
  margin-bottom: 0;
}

.coach-avatar {
  width: 140rpx; /* 增大头像 */
  height: 140rpx; /* 增大头像 */
  border-radius: 50%;
  margin-right: 30rpx;
  border: 4rpx solid #eee; /* 添加边框 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08); /* 头像阴影 */
  flex-shrink: 0;
}

.coach-gender {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40rpx; /* 增大性别图标 */
  height: 40rpx; /* 增大性别图标 */
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #eee;
}

.coach-gender image {
  width: 24rpx; /* 增大性别图标 */
  height: 24rpx; /* 增大性别图标 */
}

.coach-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.coach-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx; /* 调整间距 */
}

.coach-name {
  font-size: 34rpx; /* 增大姓名 */
  font-weight: bold;
  color: #333;
}

.coach-rating {
  display: flex;
  align-items: center;
}

.rating-score {
  font-size: 28rpx;
  color: #ff9500; /* 橙色评分 */
  margin-left: 8rpx;
}

.coach-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

.tag-item {
  background-color: #e8f0ec; /* 更沉闷的绿色背景 */
  color: #5a9178; /* 更沉闷的绿色文字 */
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.coach-description {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 最多显示2行 */
  -webkit-box-orient: vertical;
}

.coach-price-row {
  display: flex;
  justify-content: space-between;
  align-items: baseline; /* 底部对齐 */
  margin-top: 12rpx;
}

.coach-price {
  font-size: 36rpx; /* 增大价格 */
  font-weight: bold;
  color: #e64340; /* 突出价格，使用红色 */
}

.coach-price text {
  font-size: 26rpx;
  font-weight: normal;
  margin-right: 4rpx;
}

.book-btn {
  background-color: #5a9178; /* 更沉闷的绿色按钮 */
  color: #fff;
  font-size: 28rpx; /* 按钮字体 */
  padding: 12rpx 24rpx; /* 按钮内边距 */
  border-radius: 30rpx; /* 按钮圆角 */
  margin-left: 20rpx;
}

.favorite-btn {
  position: absolute; /* 绝对定位，脱离文档流 */
  top: 30rpx; /* 调整位置 */
  right: 30rpx; /* 调整位置 */
  padding: 10rpx;
  z-index: 1;
}

.favorite-btn image {
  width: 48rpx; /* 增大收藏图标 */
  height: 48rpx; /* 增大收藏图标 */
}

/* 商品瀑布流 */
.goods-waterfall {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  min-height: 200rpx;
  gap: 20rpx;
}

.goods-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.goods-item {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.goods-item:active {
  transform: scale(0.98);
}

.goods-image {
  width: 100%;
  height: auto;
  display: block;
  min-height: 200rpx;
  background-color: #f5f5f5;
  object-fit: cover;
}

/* 图片加载失败时的占位样式 */
.goods-image[src=""], .goods-image[src*="undefined"] {
  background: linear-gradient(135deg, #f5f5f5 0%, #eee 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
  position: relative;
}

.goods-image[src=""]:after, .goods-image[src*="undefined"]:after {
  content: "暂无图片";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 为不同商品设置不同的占位符高度，模拟瀑布流 */
.goods-item:nth-child(4n+1) .goods-image[src=""],
.goods-item:nth-child(4n+1) .goods-image[src*="undefined"] {
  height: 240rpx;
}

.goods-item:nth-child(4n+2) .goods-image[src=""],
.goods-item:nth-child(4n+2) .goods-image[src*="undefined"] {
  height: 300rpx;
}

.goods-item:nth-child(4n+3) .goods-image[src=""],
.goods-item:nth-child(4n+3) .goods-image[src*="undefined"] {
  height: 260rpx;
}

.goods-item:nth-child(4n+4) .goods-image[src=""],
.goods-item:nth-child(4n+4) .goods-image[src*="undefined"] {
  height: 280rpx;
}

.goods-info {
  padding: 20rpx;
  flex-shrink: 0;
}

.goods-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
  min-height: 78rpx;
}

.goods-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.goods-price {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #e64340;
}

.price-value {
  font-size: 32rpx;
  color: #e64340;
  font-weight: bold;
}

.price-original {
  font-size: 22rpx;
  color: #bbb;
  text-decoration: line-through;
  margin-left: 8rpx;
}

.goods-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.goods-sold {
  font-size: 22rpx;
  color: #999;
}

.goods-cart {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
}

.goods-cart:active {
  background-color: #e8e8e8;
}

.cart-icon {
  font-size: 28rpx;
  color: #e64340;
}

/* 保留原有课程列表样式，但隐藏不使用 */
.course-list {
  display: none;
}

/* 加载和空状态 */
.loading, .empty-tip {
  text-align: center;
  padding: 80rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 安全区区域 */
.safe-bottom {
  height: 120rpx;
}

/* 搜索圖標樣式 */
.search-box .icon {
  font-size: 28rpx;
  color: #999;
}

/* 星星圖標大小調整 */
.rating-stars .star-icon {
  width: 32rpx;
  height: 32rpx;
}

.rating-stars .star-icon::before {
  font-size: 32rpx;
}