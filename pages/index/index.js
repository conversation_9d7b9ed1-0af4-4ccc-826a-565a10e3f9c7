// pages/index/index.js
const app = getApp()

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    canIUseGetUserProfile: false,
    canIUseOpenData: wx.canIUse('open-data.type.userAvatarUrl') && wx.canIUse('open-data.type.userNickName'),
    courseList: [
      { id: 1, title: '商品1', image: '/images/goods/A/main.jpg', price: 299, originalPrice: 399, soldCount: 156 },
      { id: 2, title: '商品2', image: '/images/goods/B/main.jpg', price: 499, originalPrice: 599, soldCount: 89 },
      { id: 3, title: '商品3', image: '/images/goods/C/main.jpg', price: 399, originalPrice: 499, soldCount: 112 },
      { id: 4, title: '商品4', image: '/images/goods/D/main.jpg', price: 299, originalPrice: 399, soldCount: 78 },
      { id: 5, title: '商品5', image: '/images/goods/E/main.jpg', price: 199, originalPrice: 299, soldCount: 45 },
      { id: 6, title: '商品6', image: '/images/goods/F/main.jpg', price: 599, originalPrice: 699, soldCount: 32 },
      { id: 7, title: '商品7', image: '/images/goods/G/main.jpg', price: 259, originalPrice: 359, soldCount: 67 },
      { id: 8, title: '商品8', image: '/images/goods/H/main.jpg', price: 399, originalPrice: 499, soldCount: 54 },
      { id: 9, title: '商品9', image: '/images/goods/I/main.jpg', price: 349, originalPrice: 449, soldCount: 23 },
      { id: 10, title: '商品10', image: '/images/goods/J/main.jpg', price: 289, originalPrice: 389, soldCount: 12 }
    ], // 课程列表
    loading: false,
    searchKeyword: '' // 搜索关键词
  },

  onLoad() {
    console.log('首頁加載')
  },
  
  onShow() {
    console.log('首頁顯示')
  },
  
  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    if (!token) {
      // 未登录，仅更新状态，不强制跳转
      this.setData({
        hasUserInfo: false
      })
      return false
    }
    
    // 已登录，获取用户信息
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      })
    }
    
    return true
  },
  
  // 获取用户信息
  getUserProfile(e) {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        app.globalData.userInfo = res.userInfo
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },
  
  // 获取教练列表
  getCoachList() {
    this.setData({ loading: true })
    
    // 模拟API请求
    setTimeout(() => {
      const mockCoachList = [
        {
          id: 1,
          name: '张教练',
          avatar: '/images/avatar1.png',
          rating: 4.9,
          studentCount: 120,
          description: '国家一级台球教练，10年教学经验',
          tags: ['斯诺克', '九球', '中式八球']
        },
        {
          id: 2,
          name: '李教练',
          avatar: '/images/avatar2.png',
          rating: 4.8,
          studentCount: 98,
          description: '前职业选手，擅长九球教学',
          tags: ['九球', '美式八球']
        },
        {
          id: 3,
          name: '王教练',
          avatar: '/images/avatar3.png',
          rating: 4.7,
          studentCount: 85,
          description: '台球馆老板，教学经验丰富',
          tags: ['中式八球', '斯诺克']
        }
      ]
      
      this.setData({
        coachList: mockCoachList,
        loading: false
      })
    }, 500)
    
    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/coaches`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          this.setData({
            coachList: res.data.data,
            loading: false
          })
        } else {
          wx.showToast({
            title: res.data.message || '获取教练列表失败',
            icon: 'none'
          })
          this.setData({ loading: false })
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    })
    */
  },
  
  // 获取课程列表
  getCourseList() {
    // 模拟API请求
    setTimeout(() => {
      const mockCourseList = [
        {
          id: 1,
          title: '商品1',
          image: '/images/goods/A/main.jpg',
          price: 299,
          originalPrice: 399,
          soldCount: 156,
          coachName: '張教練',
          coachAvatar: '/images/avatar1.png'
        },
        {
          id: 2,
          title: '商品2',
          image: '/images/goods/B/main.jpg',
          price: 499,
          originalPrice: 599,
          soldCount: 89,
          coachName: '李教練',
          coachAvatar: '/images/avatar2.png'
        },
        {
          id: 3,
          title: '商品3',
          image: '/images/goods/C/main.jpg',
          price: 399,
          originalPrice: 499,
          soldCount: 112,
          coachName: '王教練',
          coachAvatar: '/images/avatar3.png'
        },
        {
          id: 4,
          title: '商品4',
          image: '/images/goods/D/main.jpg',
          price: 299,
          originalPrice: 399,
          soldCount: 78,
          coachName: '張教練',
          coachAvatar: '/images/avatar1.png'
        },
        {
          id: 5,
          title: '商品5',
          image: '/images/goods/E/main.jpg',
          price: 599,
          originalPrice: 699,
          soldCount: 67,
          coachName: '李教練',
          coachAvatar: '/images/avatar2.png'
        },
        {
          id: 6,
          title: '商品6',
          image: '/images/goods/F/main.jpg',
          price: 399,
          originalPrice: 499,
          soldCount: 92,
          coachName: '王教練',
          coachAvatar: '/images/avatar3.png'
        },
        {
          id: 7,
          title: '商品7',
          image: '/images/goods/G/main.jpg',
          price: 299,
          originalPrice: 399,
          soldCount: 167,
          coachName: '張教練',
          coachAvatar: '/images/avatar1.png'
        },
        {
          id: 8,
          title: '商品8',
          image: '/images/goods/H/main.jpg',
          price: 499,
          originalPrice: 599,
          soldCount: 134,
          coachName: '李教練',
          coachAvatar: '/images/avatar2.png'
        },
        {
          id: 9,
          title: '商品9',
          image: '/images/goods/I/main.jpg',
          price: 399,
          originalPrice: 499,
          soldCount: 88,
          coachName: '王教練',
          coachAvatar: '/images/avatar3.png'
        },
        {
          id: 10,
          title: '商品10',
          image: '/images/goods/J/main.jpg',
          price: 299,
          originalPrice: 399,
          soldCount: 123,
          coachName: '張教練',
          coachAvatar: '/images/avatar1.png'
        }
      ]
      
      this.setData({
        courseList: mockCourseList
      })
    }, 500)
  },
  
  // 搜索教练
  onSearch() {
    console.log('搜索關鍵詞：', this.data.searchKeyword)
  },
  
  // 输入搜索关键词
  onInputSearch(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },
  
  // 跳转到教练详情页
  goToCoachDetail(e) {
    const coachId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/coach/detail?id=${coachId}`
    })
  },
  
  // 跳转到预约页
  goToAppointment(e) {
    // 阻止事件冒泡，防止触发教练详情页跳转
    e.stopPropagation();
    const coachId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/appointment/detail?coachId=${coachId}`
    })
  },
  
  // 跳转到课程详情页
  goToCourseDetail(e) {
    const courseId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/course/detail?id=${courseId}`
    })
  },

  // 跳转到商城页面
  goToCourseList() {
    wx.switchTab({
      url: '/pages/mall/list'
    })
  },
  
  // 添加到购物车
  addToCart(e) {
    const id = e.currentTarget.dataset.id
    console.log('添加到購物車：', id)
    wx.showToast({
      title: '已加入購物車',
      icon: 'success'
    })
  },
  
  // 清除搜索
  onClearSearch() {
    this.setData({
      searchKeyword: ''
    })
  },
  
  // 切换收藏状态
  toggleFavorite(e) {
    // 阻止事件冒泡
    e.stopPropagation();
    const coachId = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;
    
    let coachList = this.data.coachList;
    coachList[index].isFavorite = !coachList[index].isFavorite;
    
    this.setData({
      coachList: coachList
    });
    
    // 模拟API请求，更新收藏状态
    // wx.request({
    //   url: `${app.globalData.baseUrl}/api/coach/favorite`,
    //   method: 'POST',
    //   data: { coachId: coachId, isFavorite: coachList[index].isFavorite },
    //   success: (res) => {
    //     if (res.statusCode === 200 && res.data.success) {
    //       wx.showToast({
    //         title: coachList[index].isFavorite ? '已收藏' : '已取消收藏',
    //         icon: 'success',
    //         duration: 800
    //       });
    //     }
    //   }
    // });
  },

  // 图片加载错误处理
  onImageError: function(e) {
    const { id } = e.currentTarget.dataset
    const courseList = [...this.data.courseList]
    const index = courseList.findIndex(item => item.id === id)
    
    if (index !== -1) {
      // 使用占位符显示，不更改image属性，让CSS处理显示
      courseList[index].imageLoadError = true
      this.setData({
        courseList: courseList
      })
    }
    
    console.log('图片加载失败:', e.detail)
  },

  // 图片加载成功
  onImageLoad: function(e) {
    const { id } = e.currentTarget.dataset
    const courseList = [...this.data.courseList]
    const index = courseList.findIndex(item => item.id === id)
    
    if (index !== -1) {
      courseList[index].imageLoadError = false
      this.setData({
        courseList: courseList
      })
    }
    
    console.log('图片加载成功:', e.detail)
  },
})