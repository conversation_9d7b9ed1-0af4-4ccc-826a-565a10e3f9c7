/* pages/agreement/user.wxss */

.container {
  min-height: 100vh;
  background-color: #f7fafc;
  padding: 0;
}

.agreement-content {
  background-color: white;
  margin: 25rpx;
  padding: 25rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #e2e8f0;
}

/* 为rich-text中的内容添加样式 */
.agreement-content rich-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #2d3748;
}

/* 为标题添加样式 */
.agreement-content rich-text h1 {
  font-size: 40rpx;
  font-weight: 600;
  margin: 32rpx 0 24rpx;
  color: #2d3748;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #5a9178;
}

.agreement-content rich-text h2 {
  font-size: 36rpx;
  font-weight: 600;
  margin: 24rpx 0 16rpx;
  color: #5a9178;
  position: relative;
  padding-left: 20rpx;
}

.agreement-content rich-text h2::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background-color: #5a9178;
  border-radius: 3rpx;
}

.agreement-content rich-text h3 {
  font-size: 32rpx;
  font-weight: 500;
  margin: 20rpx 0 12rpx;
  color: #2d3748;
}

/* 为段落添加样式 */
.agreement-content rich-text p {
  margin: 16rpx 0;
  text-align: justify;
  color: #4a5568;
}

/* 为列表添加样式 */
.agreement-content rich-text ul,
.agreement-content rich-text ol {
  padding-left: 32rpx;
  margin: 16rpx 0;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
}

.agreement-content rich-text li {
  margin: 12rpx 0;
  color: #4a5568;
  line-height: 1.6;
}

/* 重要条款高亮 */
.agreement-content rich-text strong {
  color: #5a9178;
  font-weight: 600;
}

/* 链接样式 */
.agreement-content rich-text a {
  color: #5a9178;
  text-decoration: none;
  border-bottom: 1rpx solid #5a9178;
}

/* 底部留白 */
.bottom-space {
  height: env(safe-area-inset-bottom, 100rpx);
  background-color: #f7fafc;
}