<!--pages/feedback/index.wxml-->
<view class="container">
  <!-- 表单区域 -->
  <view class="form-section">
    <!-- 反馈内容 -->
    <view class="form-group">
      <view class="textarea-wrapper">
        <textarea class="form-textarea"
          placeholder="请详细描述您遇到的问题或建议..."
          value="{{formData.content}}"
          bindinput="onContentInput"
          maxlength="500"
          auto-height
        ></textarea>
      </view>
      <view class="textarea-counter">{{formData.content.length}}/500</view>
    </view>

    <!-- 图片上传 -->
    <view class="form-group">
      <view class="upload-title">添加图片（选填，最多4张）</view>
      <view class="upload-wrapper">
        <!-- 已上传图片列表 -->
        <block wx:for="{{formData.images}}" wx:key="index">
          <view class="image-item">
            <image class="uploaded-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
            <view class="delete-btn" catchtap="deleteImage" data-index="{{index}}">
              <image class="delete-icon" src="/images/icon/close.png"></image>
            </view>
          </view>
        </block>
        
        <!-- 上传按钮 -->
        <view class="upload-btn" bindtap="chooseImage" wx:if="{{formData.images.length < imageConfig.maxCount}}">
          <image class="upload-icon" src="/images/icon/plus.png"></image>
        </view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="form-group">
      <view class="input-title">联系方式（选填）</view>
      <view class="input-wrapper">
        <input class="form-input" 
          type="text"
          placeholder="请输入手机号或邮箱"
          value="{{formData.contact}}"
          bindinput="onContactInput"
        />
      </view>
      <view class="input-tip">填写联系方式，以便我们及时回复您</view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="button-section">
    <button class="submit-button {{isSubmitting ? 'disabled' : ''}}" 
      bindtap="submitForm"
      disabled="{{isSubmitting}}"
    >
      提交反馈
    </button>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-bottom"></view>
</view>