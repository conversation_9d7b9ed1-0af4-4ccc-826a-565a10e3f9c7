// pages/feedback/index.js
const app = getApp()

Page({
  data: {
    // 表单数据
    formData: {
      content: '', // 反馈内容
      contact: '', // 联系方式
      images: [] // 图片列表
    },
    // 图片上传配置
    imageConfig: {
      maxCount: 4, // 最大上传数量
      sizeType: ['compressed'], // 压缩图片
      sourceType: ['album', 'camera'] // 来源：相册和相机
    },
    // 是否正在提交
    isSubmitting: false
  },

  // 输入反馈内容
  onContentInput: function (e) {
    this.setData({
      'formData.content': e.detail.value
    })
  },

  // 输入联系方式
  onContactInput: function (e) {
    this.setData({
      'formData.contact': e.detail.value
    })
  },

  // 选择图片
  chooseImage: function () {
    const { maxCount, sizeType, sourceType } = this.data.imageConfig
    const currentCount = this.data.formData.images.length
    const remainCount = maxCount - currentCount

    if (remainCount <= 0) {
      wx.showToast({
        title: `最多上传${maxCount}张图片`,
        icon: 'none'
      })
      return
    }

    wx.chooseImage({
      count: remainCount,
      sizeType: sizeType,
      sourceType: sourceType,
      success: (res) => {
        // 获取选择的图片临时路径
        const tempFilePaths = res.tempFilePaths
        
        // 更新图片列表
        const images = this.data.formData.images.concat(tempFilePaths)
        
        this.setData({
          'formData.images': images
        })
      }
    })
  },

  // 预览图片
  previewImage: function (e) {
    const { index } = e.currentTarget.dataset
    const { images } = this.data.formData

    wx.previewImage({
      current: images[index],
      urls: images
    })
  },

  // 删除图片
  deleteImage: function (e) {
    const { index } = e.currentTarget.dataset
    const images = this.data.formData.images
    
    images.splice(index, 1)
    
    this.setData({
      'formData.images': images
    })
  },

  // 上传图片
  uploadImages: function () {
    const { images } = this.data.formData
    
    if (images.length === 0) {
      return Promise.resolve([])
    }
    
    // 模拟上传过程
    return new Promise((resolve) => {
      setTimeout(() => {
        // 实际应用中，这里应该调用上传API
        // 返回上传后的图片URL列表
        const uploadedUrls = images.map(path => {
          // 这里简单返回原路径，实际应该是上传后的URL
          return path
        })
        resolve(uploadedUrls)
      }, 1000)
    })
  },

  // 验证表单
  validateForm: function () {
    const { content, contact } = this.data.formData
    
    if (!content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      })
      return false
    }
    
    if (content.length < 10) {
      wx.showToast({
        title: '反馈内容至少10个字',
        icon: 'none'
      })
      return false
    }
    
    if (contact && !this.validateContact(contact)) {
      wx.showToast({
        title: '请输入正确的手机号或邮箱',
        icon: 'none'
      })
      return false
    }
    
    return true
  },

  // 验证联系方式
  validateContact: function (contact) {
    // 手机号正则
    const phoneReg = /^1[3-9]\d{9}$/
    // 邮箱正则
    const emailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/
    
    return phoneReg.test(contact) || emailReg.test(contact)
  },

  // 提交表单
  submitForm: function () {
    // 表单验证
    if (!this.validateForm()) {
      return
    }
    
    // 防止重复提交
    if (this.data.isSubmitting) {
      return
    }
    
    this.setData({
      isSubmitting: true
    })
    
    wx.showLoading({
      title: '提交中...',
      mask: true
    })
    
    // 先上传图片
    this.uploadImages()
      .then(imageUrls => {
        // 模拟提交过程
        return new Promise((resolve) => {
          setTimeout(() => {
            // 实际应用中，这里应该调用API提交反馈
            resolve()
          }, 1000)
        })
      })
      .then(() => {
        wx.hideLoading()
        
        this.setData({
          isSubmitting: false
        })
        
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        })
        
        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      })
      .catch(error => {
        console.error('提交失败:', error)
        
        wx.hideLoading()
        
        this.setData({
          isSubmitting: false
        })
        
        wx.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        })
      })
  }
})