/* pages/feedback/index.wxss */

.container {
  min-height: 100vh;
  background-color: #f7fafc;
  padding: 24rpx;
}

/* 頁面標題 */
.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  margin-bottom: 32rpx;
  padding: 24rpx 0;
}

/* 反饋表單 */
.feedback-form {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1rpx solid #e2e8f0;
}

/* 分類選擇 */
.category-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.required-mark {
  color: #ff6b6b;
  margin-left: 8rpx;
}

.category-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.category-item {
  flex: 1;
  min-width: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 16rpx;
  background-color: #f7fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #4a5568;
  text-align: center;
}

.category-item.active {
  background-color: rgba(90, 145, 120, 0.1);
  border-color: #5a9178;
  color: #5a9178;
  font-weight: 500;
}

.category-item:active {
  transform: scale(0.98);
}

/* 評分區域 */
.rating-section {
  margin-bottom: 32rpx;
}

.rating-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: #f7fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
}

.rating-label {
  font-size: 26rpx;
  color: #4a5568;
}

.star-rating {
  display: flex;
  gap: 8rpx;
}

.star {
  font-size: 40rpx;
  color: #ddd;
  cursor: pointer;
}

.star.filled {
  color: #ffd700;
}

.star:active {
  transform: scale(1.2);
}

.rating-text {
  font-size: 24rpx;
  color: #718096;
  margin-left: 16rpx;
}

/* 反饋內容 */
.content-section {
  margin-bottom: 32rpx;
}

.textarea-container {
  position: relative;
  background-color: #f7fafc;
  border-radius: 12rpx;
  border: 2rpx solid #e2e8f0;
}

.textarea-container:focus-within {
  border-color: #5a9178;
  background-color: #ffffff;
}

.feedback-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #2d3748;
  line-height: 1.6;
  background-color: transparent;
  border-radius: 12rpx;
  box-sizing: border-box;
}

.char-count {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  font-size: 24rpx;
  color: #718096;
}

.char-count.exceeded {
  color: #ff6b6b;
}

/* 圖片上傳 */
.image-section {
  margin-bottom: 32rpx;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f7fafc;
  border: 2rpx dashed #e2e8f0;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-image {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(255, 107, 107, 0.9);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
}

.delete-image:active {
  transform: scale(0.9);
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  background-color: #f7fafc;
  border: 2rpx dashed #cbd5e0;
  border-radius: 12rpx;
  color: #718096;
}

.upload-btn:active {
  background-color: #edf2f7;
  border-color: #5a9178;
  color: #5a9178;
  transform: scale(0.98);
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 24rpx;
}

.image-tip {
  font-size: 24rpx;
  color: #718096;
  margin-top: 16rpx;
  text-align: center;
}

/* 聯繫方式 */
.contact-section {
  margin-bottom: 32rpx;
}

.contact-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: #f7fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #2d3748;
  box-sizing: border-box;
}

.contact-input:focus {
  border-color: #5a9178;
  background-color: #ffffff;
}

.contact-tip {
  font-size: 24rpx;
  color: #718096;
  margin-top: 8rpx;
  padding-left: 8rpx;
}

/* 提交按鈕 */
.submit-section {
  margin-top: 48rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #5a9178 0%, #4a7c64 100%);
  color: white;
  font-size: 30rpx;
  font-weight: 600;
  border-radius: 16rpx;
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:active {
  transform: scale(0.98);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.submit-btn:disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

.submit-btn.loading {
  opacity: 0.8;
  pointer-events: none;
}

/* 歷史反饋 */
.history-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1rpx solid #e2e8f0;
}

.history-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.view-all {
  font-size: 24rpx;
  color: #5a9178;
  font-weight: normal;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background-color: #f7fafc;
  border-radius: 12rpx;
  border-left: 4rpx solid #5a9178;
}

.history-content {
  flex: 1;
}

.history-text {
  font-size: 26rpx;
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.history-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 22rpx;
  color: #718096;
}

.history-date {
  margin-right: 16rpx;
}

.history-status {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-pending {
  background-color: rgba(255, 212, 59, 0.2);
  color: #ffd43b;
}

.status-replied {
  background-color: rgba(81, 207, 102, 0.2);
  color: #51cf66;
}

.status-closed {
  background-color: rgba(160, 174, 192, 0.2);
  color: #718096;
}

/* 空狀態 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64rpx 32rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #718096;
  line-height: 1.5;
}

/* 底部安全區域 */
.safe-bottom {
  height: env(safe-area-inset-bottom);
  background-color: #f7fafc;
}