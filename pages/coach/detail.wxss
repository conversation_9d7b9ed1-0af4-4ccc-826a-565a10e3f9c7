/* pages/coach/detail.wxss */

.container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e9ecef;
  border-top: 4rpx solid #5a9178;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 教练主要信息区域 */
.coach-main-info {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.coach-profile {
  display: flex;
  align-items: flex-start;
  position: relative;
}

.coach-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.coach-details {
  flex: 1;
}

.name-level {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.coach-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.coach-level-badge {
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.coach-level-badge.national {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.coach-level-badge.senior {
  background: linear-gradient(135deg, #5a9178, #2c6e49);
}

.coach-level-badge.intermediate {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.coach-level-badge.junior {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.venue-info {
  margin-top: 16rpx;
}

.venue-name {
  font-size: 28rpx;
  color: #5a9178;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.venue-address, .contact-phone {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 26rpx;
  color: #666;
}

.address-icon, .phone-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.favorite-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.favorite-btn image {
  width: 40rpx;
  height: 40rpx;
}

.coach-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #5a9178;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 榮譽相册 */
.honor-gallery {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.gallery-scroll {
  white-space: nowrap;
}

.gallery-list {
  display: inline-flex;
  gap: 20rpx;
}

.gallery-item {
  display: inline-block;
  width: 200rpx;
  flex-shrink: 0;
}

.honor-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}

.honor-title {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gallery-indicator {
  text-align: center;
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 課程包列表 */
.course-packages {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.package-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.package-card {
  position: relative;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
}

.package-card.hot {
  border-color: #ff6b6b;
  background: linear-gradient(135deg, #fff5f5, #ffffff);
}

.package-tag {
  position: absolute;
  top: -1rpx;
  right: 20rpx;
  background: #ff6b6b;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 0 0 12rpx 12rpx;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.package-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.package-lessons {
  font-size: 24rpx;
  color: #5a9178;
  background: #f0f9ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.package-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.current-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b6b;
  margin-right: 12rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.package-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.package-validity {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.package-stock {
  font-size: 24rpx;
  color: #ff6b6b;
  font-weight: 500;
}



/* 选择时间 - 严格按照图片UI */
.time-selection {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 日期选择器 - 严格按照图片UI */
.date-selector {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
}

.date-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 80rpx;
}

/* 今日样式 - 红色圆形背景 */
.date-item.today .date-number {
  background: #E53E3E;
  color: white;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 32rpx;
}

.date-item.today .date-week {
  color: #E53E3E;
  font-weight: 500;
  margin-top: 8rpx;
}

/* 普通日期样式 */
.date-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  min-width: 60rpx;
  text-align: center;
  line-height: 1;
}

.date-week {
  font-size: 24rpx;
  color: #666;
  line-height: 1;
}

/* 选中状态 - 非今日的选中效果 */
.date-item.active:not(.today) .date-number {
  background: #E53E3E;
  color: white;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.date-item.active:not(.today) .date-week {
  color: #E53E3E;
  font-weight: 500;
}

/* 课程时长说明 - 严格按照图片 */
.course-duration {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 30rpx;
  line-height: 1.4;
}

/* 时间段容器 - 严格按照图片布局 */
.time-slots-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

/* 时间段卡片 - 严格按照图片UI */
.time-slot-card {
  position: relative;
  width: calc((100% - 40rpx) / 3);
  height: 120rpx;
  background: #F5F5F5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

/* 可预约状态 */
.time-slot-card.available {
  background: #F5F5F5;
}

.time-slot-card.available:active {
  background: #EEEEEE;
  transform: scale(0.98);
}

/* 选中状态 */
.time-slot-card.selected {
  background: #E53E3E !important;
  color: white;
}

.time-slot-card.selected .time-text {
  color: white !important;
}

/* 已满状态 */
.time-slot-card.full {
  background: #F5F5F5;
  cursor: not-allowed;
}

/* 已过状态 */
.time-slot-card.passed {
  background: #F5F5F5;
  cursor: not-allowed;
}

/* 时间文字 */
.time-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  z-index: 2;
  position: relative;
}

/* 状态标签 - 严格按照图片位置和样式 */
.status-tag {
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 20rpx;
  color: white;
  padding: 8rpx 16rpx;
  font-weight: 500;
  z-index: 3;
}

/* 满状态标签 - 红色三角形背景，与高亮颜色一致 */
.time-slot-card.full .status-tag {
  background: linear-gradient(135deg, transparent 50%, #E53E3E 50%);
  color: white;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding: 0 8rpx 8rpx 0;
  border-radius: 0 12rpx 12rpx 0;
}

/* 过状态标签 - 灰色三角形背景 */
.time-slot-card.passed .status-tag {
  background: linear-gradient(135deg, transparent 50%, #999 50%);
  color: white;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding: 0 8rpx 8rpx 0;
  border-radius: 0 12rpx 12rpx 0;
}

/* 确认预约按钮 */
.confirm-booking-btn {
  width: 100%;
  height: 88rpx;
  background: #E53E3E;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 30rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.confirm-booking-btn:active {
  background: #C53030;
  transform: scale(0.98);
}

