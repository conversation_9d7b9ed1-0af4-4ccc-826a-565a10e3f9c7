<!--pages/coach/list.wxml-->
<view class="container">
  <!-- 搜索区域 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/images/icon-search.png"></image>
      <input 
        class="search-input" 
        placeholder="搜索教练姓名、特长等" 
        value="{{keyword}}"
        confirm-type="search"
        bindconfirm="onSearch"
      />
      <view class="search-clear" wx:if="{{keyword}}" bindtap="onClear">
        <image src="/images/icon-clear.png"></image>
      </view>
    </view>
    <view class="filter-btn" bindtap="toggleFilter">
      <image src="/images/icon-filter.png"></image>
    </view>
  </view>
  
  <!-- 排序选项 -->
  <view class="sort-container">
    <view 
      class="sort-item {{activeSort === 'default' ? 'active' : ''}}" 
      bindtap="onSortChange" 
      data-sort="default"
    >
      综合排序
    </view>
    <view 
      class="sort-item {{activeSort === 'rating' ? 'active' : ''}}" 
      bindtap="onSortChange" 
      data-sort="rating"
    >
      好评优先
    </view>
    <view class="sort-item price-sort">
      <view 
        class="{{activeSort === 'price-asc' || activeSort === 'price-desc' ? 'active' : ''}}"
        bindtap="onSortChange" 
        data-sort="{{activeSort === 'price-asc' ? 'price-desc' : 'price-asc'}}"
      >
        价格
        <view class="price-arrows">
          <view class="arrow up {{activeSort === 'price-asc' ? 'active' : ''}}"></view>
          <view class="arrow down {{activeSort === 'price-desc' ? 'active' : ''}}"></view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 筛选面板 -->
  <view class="filter-panel {{showFilter ? 'show' : ''}}">
    <view class="filter-section">
      <view class="filter-title">教练级别</view>
      <view class="filter-options">
        <view 
          class="filter-tag {{filter.level.indexOf(item.value) > -1 ? 'active' : ''}}" 
          wx:for="{{levelOptions}}" 
          wx:key="value"
          bindtap="onFilterChange"
          data-type="level"
          data-value="{{item.value}}"
        >
          {{item.text}}
        </view>
      </view>
    </view>
    
    <view class="filter-section">
      <view class="filter-title">价格区间</view>
      <view class="filter-options">
        <view 
          class="filter-tag {{filter.price.indexOf(item.value[0]) > -1 ? 'active' : ''}}" 
          wx:for="{{priceOptions}}" 
          wx:key="text"
          bindtap="onFilterChange"
          data-type="price"
          data-value="{{item.value[0]}}"
        >
          {{item.text}}
        </view>
      </view>
    </view>
    
    <view class="filter-section">
      <view class="filter-title">最低评分</view>
      <view class="filter-options">
        <view 
          class="filter-tag {{filter.rating === item.value ? 'active' : ''}}" 
          wx:for="{{ratingOptions}}" 
          wx:key="value"
          bindtap="onFilterChange"
          data-type="rating"
          data-value="{{item.value}}"
        >
          {{item.text}}
        </view>
      </view>
    </view>
    
    <view class="filter-section">
      <view class="filter-title">其他筛选</view>
      <view class="filter-options">
        <view 
          class="filter-tag {{filter.gender === 'male' ? 'active' : ''}}" 
          bindtap="onFilterChange"
          data-type="gender"
          data-value="male"
        >
          男教练
        </view>
        <view 
          class="filter-tag {{filter.gender === 'female' ? 'active' : ''}}" 
          bindtap="onFilterChange"
          data-type="gender"
          data-value="female"
        >
          女教练
        </view>
        <view 
          class="filter-tag {{filter.available ? 'active' : ''}}" 
          bindtap="onFilterChange"
          data-type="available"
          data-value="{{true}}"
        >
          可预约
        </view>
      </view>
    </view>
    
    <view class="filter-actions">
      <view class="reset-btn" bindtap="resetFilter">重置</view>
      <view class="confirm-btn" bindtap="toggleFilter">确定</view>
    </view>
  </view>
  
  <!-- 遮罩层 -->
  <view class="mask {{showFilter ? 'show' : ''}}" bindtap="toggleFilter"></view>
  
  <!-- 教练列表 -->
  <view class="coach-list {{showFilter ? 'blur' : ''}}">
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{list.length === 0}}">
      <image class="empty-icon" src="/images/empty-search.png"></image>
      <view class="empty-text">暂无符合条件的教练</view>
      <view class="empty-tips">请尝试调整筛选条件</view>
    </view>
    
    <!-- 教练列表 -->
    <block wx:else>
      <view 
        class="coach-item" 
        wx:for="{{list}}" 
        wx:key="id"
        bindtap="goToDetail"
        data-id="{{item.id}}"
      >
        <view class="coach-avatar">
          <image src="{{item.avatar}}" mode="aspectFill"></image>
          <view class="coach-gender {{item.gender}}">
            <image src="{{item.gender === 'male' ? '/images/icon-male.png' : '/images/icon-female.png'}}"></image>
          </view>
        </view>
        
        <view class="coach-info">
          <view class="coach-name-container">
            <view class="coach-name">{{item.name}}</view>
            <view class="coach-level">
              {{item.level === 'national' ? '国家级教练' : 
                item.level === 'senior' ? '高级教练' : 
                item.level === 'intermediate' ? '中级教练' : '初级教练'}}
            </view>
          </view>
          
          <view class="coach-rating">
            <view class="rating-stars">
              <block wx:for="{{5}}" wx:key="index" wx:for-item="star">
                <image 
                  wx:if="{{star <= Math.floor(item.rating)}}"
                  src="/images/star-active.png"
                ></image>
                <image 
                  wx:elif="{{star - 0.5 <= item.rating}}"
                  src="/images/star-half.png"
                ></image>
                <image 
                  wx:else
                  src="/images/star.png"
                ></image>
              </block>
            </view>
            <view class="rating-score">{{item.rating}}</view>
            <view class="rating-count">({{item.ratingCount}})</view>
          </view>
          
          <view class="coach-tags">
            <view class="tag" wx:for="{{item.tags}}" wx:key="index" wx:for-item="tag">{{tag}}</view>
          </view>
          
          <view class="coach-bottom">
            <view class="coach-price">
              <text class="price-value">¥{{item.price}}</text>
              <text class="price-unit">/小时</text>
            </view>
            
            <view class="coach-status {{item.available ? 'available' : 'unavailable'}}">
              {{item.available ? '可预约' : '已约满'}}
            </view>
          </view>
        </view>
        
        <view 
          class="favorite-btn {{item.isFavorite ? 'active' : ''}}" 
          catchtap="toggleFavorite"
          data-id="{{item.id}}"
          data-index="{{index}}"
        >
          <image src="{{item.isFavorite ? '/images/icon-favorite-active.png' : '/images/icon-favorite.png'}}"></image>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="loading-more" wx:if="{{loadingMore}}">
        <view class="loading-spinner small"></view>
        <view class="loading-text">加载更多...</view>
      </view>
      
      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!hasMore && list.length > 0}}">
        没有更多了
      </view>
    </block>
  </view>
</view>