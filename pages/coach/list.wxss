/* pages/coach/list.wxss */

.container {
  padding: 0;
  background: #f7f8fa;
  min-height: 100vh;
}

/* 搜索区域样式 */
.search-container {
  background: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 25rpx;
  padding: 0 20rpx;
  height: 70rpx;
}

.search-icon {
  font-size: 32rpx;
  color: #999;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 70rpx;
  line-height: 70rpx;
}

.clear-icon {
  font-size: 24rpx;
  color: #999;
  padding: 10rpx;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 20rpx 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 10;
}

.filter-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  min-height: 48rpx;
  flex: 1;
}

.filter-item.active {
  color: #E53E3E;
  background: rgba(229, 62, 62, 0.1);
}

.filter-item text {
  margin-right: 8rpx;
  line-height: 1;
}

.dropdown-icon {
  font-size: 20rpx;
  color: #999;
  transition: transform 0.3s ease;
  line-height: 1;
}

.dropdown-icon.rotate {
  transform: rotate(180deg);
}

.divider {
  margin: 0 10rpx;
  color: #e5e5e5;
  font-size: 28rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48rpx;
  font-weight: 300;
}

/* 搜索栏样式 */
.search-section {
  background: linear-gradient(135deg, #5a9178 0%, #4a7c64 100%);
  padding: 24rpx 32rpx 32rpx;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-wrapper {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.search-btn {
  color: #5a9178;
  font-size: 28rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background-color: rgba(90, 145, 120, 0.1);
}

.search-btn:active {
  transform: scale(0.95);
}

/* 排序栏样式 */
.sort-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx 16rpx;
  background-color: #f7fafc;
}

.sort-label {
  font-size: 26rpx;
  color: #718096;
}

.sort-options {
  display: flex;
  gap: 24rpx;
}

.sort-option {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #4a5568;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}

.sort-option.active {
  color: #5a9178;
  font-weight: 500;
  background-color: rgba(90, 145, 120, 0.1);
}

.sort-icon {
  margin-left: 8rpx;
  font-size: 20rpx;
}

/* 教练列表样式 */
.coach-list {
  padding: 20rpx;
}

.coach-grid {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.coach-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.coach-card {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.coach-avatar {
  width: 100%;
  height: 320rpx;
  background: #f5f5f5;
}

.coach-info {
  padding: 16rpx;
}

.coach-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.venue-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.venue-name {
  font-size: 24rpx;
  color: #666;
}

.venue-address {
  font-size: 22rpx;
  color: #999;
}

.coach-title {
  font-size: 26rpx;
  color: #5a9178;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.coach-rating {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.rating-stars {
  display: flex;
  margin-right: 8rpx;
}

.star {
  font-size: 24rpx;
  color: #ddd;
  margin-right: 2rpx;
}

.star.filled {
  color: #ffd700;
}

.rating-text {
  font-size: 24rpx;
  color: #718096;
}

.coach-price {
  display: flex;
  align-items: baseline;
  margin-top: 8rpx;
}

.price-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b6b;
  margin-right: 8rpx;
}

.price-unit {
  font-size: 24rpx;
  color: #718096;
}

/* 教练身体信息 */
.coach-body {
  padding: 24rpx 32rpx;
}

.coach-specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.specialty-tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: rgba(90, 145, 120, 0.1);
  color: #5a9178;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: 1rpx solid rgba(90, 145, 120, 0.2);
}

.coach-description {
  font-size: 26rpx;
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 24rpx;
}

.coach-stats {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 0;
  border-top: 1rpx solid #e2e8f0;
  border-bottom: 1rpx solid #e2e8f0;
  margin-bottom: 24rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #718096;
}

/* 教练底部操作 */
.coach-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #f7fafc;
}

.coach-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background-color: #51cf66;
  margin-right: 8rpx;
}

.status-indicator.busy {
  background-color: #ff6b6b;
}

.status-text {
  font-size: 24rpx;
  color: #4a5568;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.contact-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: #ffffff;
  color: #5a9178;
  border: 2rpx solid #5a9178;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.contact-btn:active {
  background-color: #5a9178;
  color: white;
  transform: scale(0.95);
}

.book-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: #5a9178;
  color: white;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.book-btn:active {
  background-color: #4a7c64;
  transform: scale(0.95);
}

/* 加载更多样式 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
  color: #718096;
  font-size: 26rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #e2e8f0;
  border-top: 3rpx solid #5a9178;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
  padding: 100rpx 0;
}

/* 加載狀態 */
.van-loading {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
}

.search-area {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #fff;
  padding: 8rpx 0;
}

/* 下拉菜單樣式 */
.filter-menu {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.menu-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  padding: 0 20rpx;
  position: relative;
}

.menu-item text {
  margin-right: 4rpx;
}

.menu-divider {
  color: #ddd;
  font-size: 24rpx;
  padding: 0 10rpx;
}

/* 下拉選項樣式 */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  max-height: 500rpx;
  overflow-y: auto;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
  z-index: 100;
  padding: 20rpx;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* 省份下拉選單專用網格佈局 */
.filter-item[data-type="province"] .dropdown-menu {
  position: fixed;
  top: 225rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 92vw;
  max-width: 680rpx;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(7, auto);
  gap: 10rpx;
  padding: 24rpx;
  max-height: none;
  height: auto;
  overflow: visible;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  background: #fff;
  z-index: 1000;
  /* 保持原來的顯示/隱藏邏輯 */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

/* 省份下拉選單顯示狀態 */
.filter-item[data-type="province"] .dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

.filter-item[data-type="province"] .dropdown-menu .dropdown-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 8rpx;
  text-align: center;
  border: none;
  border-radius: 0;
  font-size: 24rpx;
  color: #444;
  background: transparent;
  transition: all 0.25s ease;
  box-sizing: border-box;
  min-height: 60rpx;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}

/* 省份網格中每行添加分隔符，除了每行最後一個元素 */
.filter-item[data-type="province"] .dropdown-menu .dropdown-item:not(:nth-child(5n))::after {
  content: "|";
  position: absolute;
  right: -5rpx;
  color: #ddd;
  font-size: 20rpx;
}

.filter-item[data-type="province"] .dropdown-menu .dropdown-item:hover {
  background: #f0f7ff;
  color: #2563eb;
  border-radius: 6rpx;
}

.filter-item[data-type="province"] .dropdown-menu .dropdown-item.active {
  color: #fff;
  background: #E53E3E;
  border-radius: 6rpx;
  font-weight: 500;
}

/* "不限"選項特殊樣式 */
.filter-item[data-type="province"] .dropdown-menu .dropdown-item[data-value=""] {
  background: #f0f8ff;
  border-color: #d4edda;
  color: #155724;
  font-weight: 500;
}

.filter-item[data-type="province"] .dropdown-menu .dropdown-item[data-value=""]:hover {
  background: #e6f3ff;
  border-color: #bee5eb;
  color: #0c5460;
}

/* 級別下拉選單專用網格佈局 (4個選項+不限 = 5個，1行) */
.filter-item[data-type="level"] .dropdown-menu {
  position: fixed;
  top: 225rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 92vw;
  max-width: 680rpx;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10rpx;
  padding: 24rpx;
  max-height: none;
  height: auto;
  overflow: visible;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  background: #fff;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-item[data-type="level"] .dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

/* 種類下拉選單專用網格佈局 (3個選項+不限 = 4個，1行) */
.filter-item[data-type="category"] .dropdown-menu {
  position: fixed;
  top: 225rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 70vw;
  max-width: 480rpx;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10rpx;
  padding: 24rpx;
  max-height: none;
  height: auto;
  overflow: visible;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  background: #fff;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-item[data-type="category"] .dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

/* 性別下拉選單專用網格佈局 (2個選項+不限 = 3個，1行) */
.filter-item[data-type="gender"] .dropdown-menu {
  position: fixed;
  top: 225rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 50vw;
  max-width: 360rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10rpx;
  padding: 24rpx;
  max-height: none;
  height: auto;
  overflow: visible;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  background: #fff;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-item[data-type="gender"] .dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

/* 統一的下拉選項樣式 (適用於級別、種類、性別) */
.filter-item[data-type="level"] .dropdown-menu .dropdown-item,
.filter-item[data-type="category"] .dropdown-menu .dropdown-item,
.filter-item[data-type="gender"] .dropdown-menu .dropdown-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 8rpx;
  text-align: center;
  border: none;
  border-radius: 0;
  font-size: 24rpx;
  color: #444;
  background: transparent;
  transition: all 0.25s ease;
  box-sizing: border-box;
  min-height: 60rpx;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}

/* 添加分隔符 "|" 到非最後一個選項 */
.filter-item[data-type="level"] .dropdown-menu .dropdown-item:not(:last-child)::after,
.filter-item[data-type="category"] .dropdown-menu .dropdown-item:not(:last-child)::after,
.filter-item[data-type="gender"] .dropdown-menu .dropdown-item:not(:last-child)::after {
  content: "|";
  position: absolute;
  right: -10rpx;
  color: #ddd;
  font-size: 20rpx;
}

.filter-item[data-type="level"] .dropdown-menu .dropdown-item:hover,
.filter-item[data-type="category"] .dropdown-menu .dropdown-item:hover,
.filter-item[data-type="gender"] .dropdown-menu .dropdown-item:hover {
  background: #f0f7ff;
  color: #2563eb;
  border-radius: 6rpx;
}

.filter-item[data-type="level"] .dropdown-menu .dropdown-item.active,
.filter-item[data-type="category"] .dropdown-menu .dropdown-item.active,
.filter-item[data-type="gender"] .dropdown-menu .dropdown-item.active {
  color: #fff;
  background: #E53E3E;
  border-radius: 6rpx;
  font-weight: 500;
}

/* "不限"選項特殊樣式 (適用於級別、種類、性別) */
.filter-item[data-type="level"] .dropdown-menu .dropdown-item[data-value=""],
.filter-item[data-type="category"] .dropdown-menu .dropdown-item[data-value=""],
.filter-item[data-type="gender"] .dropdown-menu .dropdown-item[data-value=""] {
  background: #f0f8ff;
  border-color: #d4edda;
  color: #155724;
  font-weight: 500;
}

.filter-item[data-type="level"] .dropdown-menu .dropdown-item[data-value=""]:hover,
.filter-item[data-type="category"] .dropdown-menu .dropdown-item[data-value=""]:hover,
.filter-item[data-type="gender"] .dropdown-menu .dropdown-item[data-value=""]:hover {
  background: #e6f3ff;
  border-color: #bee5eb;
  color: #0c5460;
}

/* 下拉菜单遮罩 */
.dropdown-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 50;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.dropdown-mask.show {
  opacity: 1;
  visibility: visible;
}


