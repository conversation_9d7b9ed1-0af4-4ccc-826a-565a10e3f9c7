<!--pages/coach/detail.wxml-->
<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <block wx:else>
    <!-- 教练主要信息区域 -->
    <view class="coach-main-info">
      <view class="coach-profile">
        <image class="coach-avatar" src="{{coach.avatar}}" mode="aspectFill" bindtap="previewAvatar"></image>
        <view class="coach-details">
          <view class="name-level">
            <text class="coach-name">{{coach.name}}</text>
            <view class="coach-level-badge {{coach.levelClass}}">{{coach.levelText}}</view>
          </view>
          <view class="venue-info">
            <view class="venue-name" bindtap="goToVenue">{{coach.venue.name}}</view>
            <view class="venue-address" bindtap="openMap">
              <text class="address-icon">📍</text>
              <text class="address-text">{{coach.venue.address}}</text>
            </view>
            <view class="contact-phone" bindtap="makeCall">
              <text class="phone-icon">📞</text>
              <text class="phone-text">{{coach.phone}}</text>
            </view>
          </view>
        </view>
        <view 
          class="favorite-btn {{coach.isFavorite ? 'active' : ''}}" 
          bindtap="toggleFavorite"
        >
          <image src="{{coach.isFavorite ? '/images/icon-favorite-active.png' : '/images/icon-favorite.png'}}"></image>
        </view>
      </view>
      
      <view class="coach-stats">
        <view class="stat-item">
          <text class="stat-value">{{coach.rating}}</text>
          <text class="stat-label">評分</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{coach.studentCount}}</text>
          <text class="stat-label">學員</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{coach.experience}}</text>
          <text class="stat-label">經驗</text>
        </view>
      </view>
    </view>

    <!-- 教练荣誉相册 -->
    <view class="honor-gallery" wx:if="{{coach.honors && coach.honors.length > 0}}">
      <view class="section-title">榮譽相册</view>
      <scroll-view class="gallery-scroll" scroll-x>
        <view class="gallery-list">
          <view 
            class="gallery-item" 
            wx:for="{{coach.honors}}" 
            wx:key="id"
            bindtap="previewHonor"
            data-index="{{index}}"
          >
            <image class="honor-image" src="{{item.thumbnail}}" mode="aspectFill"></image>
            <view class="honor-title">{{item.title}}</view>
          </view>
        </view>
      </scroll-view>
      <view class="gallery-indicator">{{coach.honors.length}}張照片</view>
    </view>

    <!-- 课程包列表 -->
    <view class="course-packages">
      <view class="section-title">課程包選擇</view>
      <view class="package-list">
        <view 
          class="package-card {{item.isHot ? 'hot' : ''}}" 
          wx:for="{{coursePackages}}" 
          wx:key="id"
          bindtap="selectPackage"
          data-package="{{item}}"
        >
          <view class="package-tag" wx:if="{{item.isHot}}">熱門</view>
          <view class="package-header">
            <text class="package-name">{{item.name}}</text>
            <text class="package-lessons">{{item.lessons}}節課</text>
          </view>
          <view class="package-price">
            <text class="current-price">¥{{item.currentPrice}}</text>
            <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
          </view>
          <view class="package-desc">{{item.description}}</view>
          <view class="package-validity">有效期：{{item.validity}}個月</view>
          <view class="package-stock" wx:if="{{item.stock < 10}}">僅剩{{item.stock}}份</view>
        </view>
      </view>
    </view>

    <!-- 选择时间 -->
    <view class="time-selection">
      <view class="section-title">選擇時間</view>
      
      <!-- 日期选择器 - 严格按照图片UI -->
      <view class="date-selector">
        <view 
          class="date-item {{selectedDate === item.date ? 'active' : ''}} {{item.isToday ? 'today' : ''}}"
          wx:for="{{dateList}}" 
          wx:key="date"
          bindtap="selectDate"
          data-date="{{item.date}}"
        >
          <view class="date-number">{{item.day}}</view>
          <view class="date-week">{{item.weekText}}</view>
        </view>
      </view>
      
      <!-- 课程时长说明 - 严格按照图片 -->
      <view class="course-duration">課程時長90分鐘(請選擇起始時間)</view>
      
      <!-- 时间段卡片 - 严格按照图片UI -->
      <view class="time-slots-container">
        <view 
          class="time-slot-card {{timeSlot.status}} {{timeSlot.selected ? 'selected' : ''}}"
          wx:for="{{selectedTimeSlots}}" 
          wx:key="time"
          wx:for-item="timeSlot"
          bindtap="selectTimeSlot"
          data-time="{{timeSlot.time}}"
          data-index="{{index}}"
        >
          <view class="time-text">{{timeSlot.time}}</view>
          <view class="status-tag" wx:if="{{timeSlot.status !== 'available'}}">
            {{timeSlot.statusText}}
          </view>
        </view>
      </view>
      
      <!-- 确认预约按钮 -->
      <view class="confirm-booking-btn" bindtap="confirmBooking" wx:if="{{hasSelectedSlots}}">
        確認預約
      </view>
    </view>
  </block>
</view>