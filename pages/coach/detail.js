// pages/coach/detail.js
const app = getApp()

Page({
  data: {
    id: '',
    loading: true,
    coach: {},
    coursePackages: [],
    
    // 时间选择相关数据
    dateList: [],
    selectedDate: '',
    selectedTimeSlots: [],
    hasSelectedSlots: false
  },

  onLoad: function(options) {
    this.setData({
      id: options.id || '1'
    })
    
    this.loadCoachData()
    this.initCoursePackages()
    this.loadDateList()
  },
  
  // 加载教练数据
  loadCoachData: function() {
    // 模拟API调用
    setTimeout(() => {
      this.setData({
        coach: {
          id: this.data.id,
          name: '張教練',
          avatar: '/images/coach-avatar.jpg',
          level: 'professional',
          levelText: '專業教練',
          levelClass: 'professional',
          rating: 4.8,
          studentCount: 156,
          experience: '5年',
          phone: '138****8888',
          isFavorite: false,
          venue: {
            id: 1,
            name: '星光網球俱樂部',
            address: '台北市信義區信義路五段7號',
            latitude: 25.033,
            longitude: 121.565
          },
          honors: [
            {
              id: 1,
              title: '2023年度最佳教練',
              thumbnail: '/images/honor1-thumb.jpg',
              fullImage: '/images/honor1-full.jpg'
            },
            {
              id: 2,
              title: 'ITF認證教練',
              thumbnail: '/images/honor2-thumb.jpg',
              fullImage: '/images/honor2-full.jpg'
            }
          ]
        },
        loading: false
      })
    }, 1000)
  },
  
  // 初始化课程包数据
  initCoursePackages: function() {
    this.setData({
      coursePackages: [
        {
          id: 1,
          name: '20節私教卡',
          lessons: 20,
          currentPrice: 5600,
          originalPrice: 6000,
          description: '適合基礎提升，包含基本技術訓練',
          validity: 6,
          stock: 15,
          isHot: true
        },
        {
          id: 2,
          name: '50節私教卡',
          lessons: 50,
          currentPrice: 13500,
          originalPrice: 15000,
          description: '適合系統學習，包含全套技術訓練',
          validity: 12,
          stock: 8,
          isHot: false
        },
        {
          id: 3,
          name: '10節體驗卡',
          lessons: 10,
          currentPrice: 2800,
          originalPrice: 3000,
          description: '適合初學者體驗，短期快速入門',
          validity: 3,
          stock: 5,
          isHot: false
        }
      ]
    })
  },
  
  // 加载日期列表 - 按照图片UI设计
  loadDateList: function() {
    const today = new Date()
    const dateList = []
    
    // 生成未来7天的日期，严格按照图片格式
    for (let i = 0; i < 7; i++) {
      const date = new Date(today)
      date.setDate(date.getDate() + i)
      
      const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
      const day = String(date.getDate())
      
      let weekText
      if (i === 0) {
        weekText = '今日'
      } else {
        const weekNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        weekText = weekNames[date.getDay()]
      }
      
      dateList.push({
        date: dateStr,
        day: day,
        weekText: weekText,
        isToday: i === 0
      })
    }
    
    // 设置默认选中今天
    const todayStr = dateList[0].date
    
    this.setData({
      dateList: dateList,
      selectedDate: todayStr
    })
    
    // 生成今天的时间段数据
    this.generateTimeSlots(0)
  },
  
  // 生成时间段数据 - 按照图片UI设计
  generateTimeSlots: function(dayIndex) {
    const timeSlots = [
      '08:30', '10:00', '13:00', '14:30', '16:00',
      '18:00', '19:30'
    ]
    
    const slots = timeSlots.map((time, index) => {
      let status = 'available'
      let statusText = ''
      let selected = false
      
      // 如果是今天，检查时间是否已过
      if (dayIndex === 0 && this.isTimePassed(time)) {
        status = 'passed'
        statusText = '過'
      } else {
        // 随机分配状态，按照图片显示的状态
        const random = Math.random()
        if (random > 0.7) {
          status = 'full'
          statusText = '滿'
        } else if (random > 0.2) {
          status = 'available'
          statusText = ''
        } else {
          status = 'passed'
          statusText = '過'
        }
      }
      
      return {
        time: time,
        status: status,
        statusText: statusText,
        selected: selected
      }
    })
    
    this.setData({
      selectedTimeSlots: slots,
      hasSelectedSlots: false
    })
  },
  
  // 检查时间是否已过
  isTimePassed: function(timeStr) {
    const now = new Date()
    const [hour, minute] = timeStr.split(':').map(Number)
    const timeDate = new Date()
    timeDate.setHours(hour, minute, 0, 0)
    
    return now > timeDate
  },
  
  // 选择日期
  selectDate: function(e) {
    const { date } = e.currentTarget.dataset
    const dateIndex = this.data.dateList.findIndex(item => item.date === date)
    
    this.setData({
      selectedDate: date
    })
    
    // 重新生成该日期的时间段数据
    this.generateTimeSlots(dateIndex)
  },
  
  // 选择时间段 - 支持多选和高亮
  selectTimeSlot: function(e) {
    const { time, index } = e.currentTarget.dataset
    const timeSlots = [...this.data.selectedTimeSlots]
    const timeSlot = timeSlots[index]
    
    if (timeSlot.status !== 'available') {
      wx.showToast({
        title: timeSlot.status === 'full' ? '該時段已滿' : '該時段已過',
        icon: 'none'
      })
      return
    }
    
    // 切换选中状态
    timeSlot.selected = !timeSlot.selected
    
    // 检查是否有选中的时间段
    const hasSelected = timeSlots.some(slot => slot.selected)
    
    this.setData({
      selectedTimeSlots: timeSlots,
      hasSelectedSlots: hasSelected
    })
  },
  
  // 预览教练头像
  previewAvatar: function() {
    wx.previewImage({
      current: this.data.coach.avatar,
      urls: [this.data.coach.avatar]
    })
  },
  
  // 预览荣誉照片
  previewHonor: function(e) {
    const { index } = e.currentTarget.dataset
    const urls = this.data.coach.honors.map(item => item.fullImage)
    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  },
  
  // 打开地图导航
  openMap: function() {
    const { latitude, longitude, name, address } = this.data.coach.venue
    wx.openLocation({
      latitude: latitude,
      longitude: longitude,
      name: name,
      address: address
    })
  },
  
  // 一键拨号
  makeCall: function() {
    wx.makePhoneCall({
      phoneNumber: this.data.coach.phone.replace(/\*/g, '1') // 替换星号为实际数字
    })
  },
  
  // 跳转到球馆详情
  goToVenue: function() {
    // 跳转到球馆详情页面
    wx.navigateTo({
      url: `/pages/venue/detail?id=${this.data.coach.venue.id}`
    })
  },
  
  // 切换收藏状态
  toggleFavorite: function() {
    const isFavorite = !this.data.coach.isFavorite
    this.setData({
      'coach.isFavorite': isFavorite
    })
    
    wx.showToast({
      title: isFavorite ? '已收藏' : '已取消收藏',
      icon: 'success'
    })
  },
  
  // 选择课程包
  selectPackage: function(e) {
    const { package: selectedPackage } = e.currentTarget.dataset
    
    wx.showModal({
      title: '確認購買',
      content: `您選擇了${selectedPackage.name}，價格：¥${selectedPackage.currentPrice}，是否確認購買？`,
      success: (res) => {
        if (res.confirm) {
          // 跳转到支付页面
          wx.navigateTo({
            url: `/pages/order/create?coachId=${this.data.id}&packageId=${selectedPackage.id}`
          })
        }
      }
    })
  },
  
  // 确认预约
  confirmBooking: function() {
    const selectedSlots = this.data.selectedTimeSlots.filter(slot => slot.selected)
    
    if (selectedSlots.length === 0) {
      wx.showToast({
        title: '請選擇時間段',
        icon: 'none'
      })
      return
    }
    
    const selectedDate = this.data.dateList.find(item => item.date === this.data.selectedDate)
    const slotsText = selectedSlots.map(slot => 
      `${selectedDate.weekText} ${slot.time}`
    ).join('\n')
    
    wx.showModal({
      title: '確認預約',
      content: `確認預約以下時間段嗎？\n\n${slotsText}\n\n課程時長：90分鐘`,
      success: (res) => {
        if (res.confirm) {
          // 跳转到预约确认页面
          const bookingData = {
            coachId: this.data.id,
            date: this.data.selectedDate,
            timeSlots: selectedSlots.map(slot => slot.time)
          }
          
          wx.navigateTo({
            url: `/pages/appointment/create?data=${encodeURIComponent(JSON.stringify(bookingData))}`
          })
        }
      }
    })
  },
})