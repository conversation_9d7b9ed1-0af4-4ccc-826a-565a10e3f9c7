<!--pages/coach/list.wxml-->
<view class="container">
  <!-- 搜索区域 -->
  <view class="search-container">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索教练姓名、特长等" 
        value="{{keyword}}"
        bindinput="onInputSearch"
        bindconfirm="onSearch"
        confirm-type="search"
      />
      <text wx:if="{{keyword}}" class="clear-icon" bindtap="onClear">✖</text>
    </view>
  </view>
  
  <!-- 筛选选项行 -->
  <view class="filter-bar">
    <view class="filter-item {{filter.province ? 'active' : ''}}" bindtap="toggleDropdown" data-type="province">
      <text>{{selectedProvinceText === '不限' ? '省份' : (selectedProvinceText || '省份')}}</text>
      <text class="dropdown-icon {{showDropdown === 'province' ? 'rotate' : ''}}">▼</text>
      
      <!-- 省份下拉菜单 -->
      <view class="dropdown-menu {{showDropdown === 'province' ? 'show' : ''}}" catchtap="stopPropagation">
        <view 
          class="dropdown-item {{filter.province === '' && selectedProvinceText === '不限' ? 'active' : ''}}"
          bindtap="onFilterSelect"
          data-type="province"
          data-value=""
          data-text="不限"
        >
          不限
        </view>
        <view 
          class="dropdown-item {{filter.province === item.value ? 'active' : ''}}"
          wx:for="{{provinceOptions}}" 
          wx:key="value"
          bindtap="onFilterSelect"
          data-type="province"
          data-value="{{item.value}}"
          data-text="{{item.text}}"
        >
          {{item.text}}
        </view>
      </view>
    </view>
    
    <text class="divider">|</text>
    
    <view class="filter-item {{filter.level ? 'active' : ''}}" bindtap="toggleDropdown" data-type="level">
      <text>{{selectedLevelText === '不限' ? '级别' : (selectedLevelText || '级别')}}</text>
      <text class="dropdown-icon {{showDropdown === 'level' ? 'rotate' : ''}}">▼</text>
      
      <!-- 级别下拉菜单 -->
      <view class="dropdown-menu {{showDropdown === 'level' ? 'show' : ''}}" catchtap="stopPropagation">
        <view 
          class="dropdown-item {{filter.level === '' && selectedLevelText === '不限' ? 'active' : ''}}"
          bindtap="onFilterSelect"
          data-type="level"
          data-value=""
          data-text="不限"
        >
          不限
        </view>
        <view 
          class="dropdown-item {{filter.level === item.value ? 'active' : ''}}"
          wx:for="{{levelOptions}}" 
          wx:key="value"
          bindtap="onFilterSelect"
          data-type="level"
          data-value="{{item.value}}"
          data-text="{{item.text}}"
        >
          {{item.text}}
        </view>
      </view>
    </view>
    
    <text class="divider">|</text>
    
    <view class="filter-item {{filter.category ? 'active' : ''}}" bindtap="toggleDropdown" data-type="category">
      <text>{{selectedCategoryText === '不限' ? '种类' : (selectedCategoryText || '种类')}}</text>
      <text class="dropdown-icon {{showDropdown === 'category' ? 'rotate' : ''}}">▼</text>
      
      <!-- 种类下拉菜单 -->
      <view class="dropdown-menu {{showDropdown === 'category' ? 'show' : ''}}" catchtap="stopPropagation">
        <view 
          class="dropdown-item {{filter.category === '' && selectedCategoryText === '不限' ? 'active' : ''}}"
          bindtap="onFilterSelect"
          data-type="category"
          data-value=""
          data-text="不限"
        >
          不限
        </view>
        <view 
          class="dropdown-item {{filter.category === item.value ? 'active' : ''}}"
          wx:for="{{categoryOptions}}" 
          wx:key="value"
          bindtap="onFilterSelect"
          data-type="category"
          data-value="{{item.value}}"
          data-text="{{item.text}}"
        >
          {{item.text}}
        </view>
      </view>
    </view>
    
    <text class="divider">|</text>
    
    <view class="filter-item {{filter.gender ? 'active' : ''}}" bindtap="toggleDropdown" data-type="gender">
      <text>{{selectedGenderText === '不限' ? '性别' : (selectedGenderText || '性别')}}</text>
      <text class="dropdown-icon {{showDropdown === 'gender' ? 'rotate' : ''}}">▼</text>
      
      <!-- 性别下拉菜单 -->
      <view class="dropdown-menu {{showDropdown === 'gender' ? 'show' : ''}}" catchtap="stopPropagation">
        <view 
          class="dropdown-item {{filter.gender === '' && selectedGenderText === '不限' ? 'active' : ''}}"
          bindtap="onFilterSelect"
          data-type="gender"
          data-value=""
          data-text="不限"
        >
          不限
        </view>
        <view 
          class="dropdown-item {{filter.gender === item.value ? 'active' : ''}}"
          wx:for="{{genderOptions}}" 
          wx:key="value"
          bindtap="onFilterSelect"
          data-type="gender"
          data-value="{{item.value}}"
          data-text="{{item.text}}"
        >
          {{item.text}}
        </view>
      </view>
    </view>
  </view>
  
  <!-- 下拉菜单遮罩层 -->
  <view class="dropdown-mask {{showDropdown ? 'show' : ''}}" bindtap="hideDropdown"></view>
  

  
  <!-- 教练列表 -->
  <view class="coach-list {{showFilter ? 'blur' : ''}}">
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{list.length === 0}}">
      <image class="empty-icon" src="/images/empty-search.png"></image>
      <view class="empty-text">暂无符合条件的教练</view>
      <view class="empty-tips">请尝试调整筛选条件</view>
    </view>
    
    <!-- 教练列表 -->
    <block wx:else>
      <view class="coach-grid">
        <view class="coach-column">
          <block wx:for="{{list}}" wx:key="id" wx:if="{{index % 2 === 0}}">
            <view class="coach-card" bindtap="goToDetail" data-id="{{item.id}}">
              <image class="coach-avatar" src="{{item.avatar}}" mode="aspectFill" binderror="onImageError"></image>
              <view class="coach-info">
                <view class="coach-name-row">
                  <view class="coach-name">{{item.name}}</view>
                  <view class="coach-level-tag {{item.levelClass}}">{{item.levelText}}</view>
                </view>
                <view class="venue-info">
                  <view class="venue-name">上海市利都桌球俱乐部</view>
                  <view class="venue-address">上海市杨浦区本溪路577弄3F</view>
                </view>
              </view>
            </view>
          </block>
        </view>
        <view class="coach-column">
          <block wx:for="{{list}}" wx:key="id" wx:if="{{index % 2 === 1}}">
            <view class="coach-card" bindtap="goToDetail" data-id="{{item.id}}">
              <image class="coach-avatar" src="{{item.avatar}}" mode="aspectFill" binderror="onImageError"></image>
              <view class="coach-info">
                <view class="coach-name-row">
                  <view class="coach-name">{{item.name}}</view>
                  <view class="coach-level-tag {{item.levelClass}}">{{item.levelText}}</view>
                </view>
                <view class="venue-info">
                  <view class="venue-name">上海市利都桌球俱乐部</view>
                  <view class="venue-address">上海市杨浦区本溪路577弄3F</view>
                </view>
              </view>
            </view>
          </block>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMore}}">
        <view class="load-more-spinner" wx:if="{{loadingMore}}"></view>
        <view class="load-more-text">{{loadingMore ? '加载中...' : '上拉加载更多'}}</view>
      </view>
      
      <!-- 没有更多 -->
      <view class="no-more" wx:else>
        <view class="no-more-text">已显示全部{{total}}位教练</view>
      </view>
    </block>
  </view>
</view>