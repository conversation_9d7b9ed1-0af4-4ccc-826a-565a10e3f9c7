// pages/coach/list.js
const app = getApp()

Page({
  data: {
    // 页面状态
    loading: true,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    
    // 搜索和筛选
    keyword: '',
    showFilter: false,
    activeSort: 'default', // default, rating, price-asc, price-desc
    filter: {
      level: [], // 教练级别
      price: [], // 价格区间
      rating: null, // 最低评分
      gender: '', // 性别筛选
      available: false // 是否只看可预约
    },
    
    // 筛选选项
    levelOptions: [
      { text: '国家级教练', value: 'national' },
      { text: '高级教练', value: 'senior' },
      { text: '中级教练', value: 'intermediate' },
      { text: '初级教练', value: 'junior' }
    ],
    priceOptions: [
      { text: '100元以下', value: [0, 100] },
      { text: '100-200元', value: [100, 200] },
      { text: '200-300元', value: [200, 300] },
      { text: '300元以上', value: [300, 999999] }
    ],
    ratingOptions: [
      { text: '4.5分以上', value: 4.5 },
      { text: '4分以上', value: 4 },
      { text: '3.5分以上', value: 3.5 }
    ],
    
    // 列表数据
    page: 1,
    pageSize: 10,
    total: 0,
    list: []
  },

  onLoad: function (options) {
    // 处理分享或扫码进入的场景
    if (options.keyword) {
      this.setData({ keyword: options.keyword })
    }
    
    // 加载教练列表
    this.loadCoachList()
  },

  onShow: function () {
    // 可能需要刷新列表（如收藏状态变化）
    if (this.data.list.length > 0) {
      this.updateCoachList()
    }
  },
  
  // 下拉刷新
  onPullDownRefresh: function () {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    }, () => {
      this.loadCoachList().then(() => {
        wx.stopPullDownRefresh()
        this.setData({ refreshing: false })
      })
    })
  },
  
  // 上拉加载更多
  onReachBottom: function () {
    if (this.data.loadingMore || !this.data.hasMore) return
    
    this.setData({
      page: this.data.page + 1,
      loadingMore: true
    }, () => {
      this.loadCoachList(true)
    })
  },
  
  // 加载教练列表
  loadCoachList: function (loadMore = false) {
    if (!loadMore) {
      this.setData({ loading: true })
    }
    
    // 构建请求参数
    const params = {
      page: this.data.page,
      pageSize: this.data.pageSize,
      keyword: this.data.keyword,
      sort: this.data.activeSort,
      ...this.data.filter
    }
    
    // 模拟API请求
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟数据
        const mockList = Array(10).fill(0).map((_, index) => ({
          id: loadMore ? this.data.list.length + index + 1 : index + 1,
          name: `教练${loadMore ? this.data.list.length + index + 1 : index + 1}`,
          avatar: '/images/default-avatar.png',
          level: this.data.levelOptions[Math.floor(Math.random() * 4)].value,
          gender: Math.random() > 0.5 ? 'male' : 'female',
          rating: (4 + Math.random()).toFixed(1),
          ratingCount: Math.floor(Math.random() * 1000),
          price: Math.floor(Math.random() * 400 + 100),
          tags: ['技术过硬', '耐心细致', '经验丰富'].slice(0, Math.floor(Math.random() * 3) + 1),
          available: Math.random() > 0.3,
          isFavorite: Math.random() > 0.7
        }))
        
        this.setData({
          loading: false,
          loadingMore: false,
          list: loadMore ? [...this.data.list, ...mockList] : mockList,
          total: 100,
          hasMore: this.data.page * this.data.pageSize < 100
        })
        
        resolve()
      }, 1000)
    })
    
    // 实际API请求示例
    /*
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.baseUrl}/api/coach/list`,
        method: 'GET',
        data: params,
        success: (res) => {
          if (res.statusCode === 200 && res.data.success) {
            this.setData({
              loading: false,
              loadingMore: false,
              list: loadMore ? [...this.data.list, ...res.data.data.list] : res.data.data.list,
              total: res.data.data.total,
              hasMore: this.data.page * this.data.pageSize < res.data.data.total
            })
          }
          resolve()
        },
        fail: reject
      })
    })
    */
  },
  
  // 更新教练列表（如收藏状态变化）
  updateCoachList: function() {
    // 实际项目中，这里应该调用API获取最新的收藏状态
  },
  
  // 搜索相关方法
  onSearch: function(e) {
    this.setData({
      keyword: e.detail,
      page: 1,
      hasMore: true
    }, () => {
      this.loadCoachList()
    })
  },
  
  onClear: function() {
    this.setData({
      keyword: '',
      page: 1,
      hasMore: true
    }, () => {
      this.loadCoachList()
    })
  },
  
  // 筛选相关方法
  toggleFilter: function() {
    this.setData({
      showFilter: !this.data.showFilter
    })
  },
  
  onSortChange: function(e) {
    const sort = e.currentTarget.dataset.sort
    if (sort === this.data.activeSort) return
    
    this.setData({
      activeSort: sort,
      page: 1,
      hasMore: true
    }, () => {
      this.loadCoachList()
    })
  },
  
  onFilterChange: function(e) {
    const { type, value } = e.currentTarget.dataset
    let newFilter = { ...this.data.filter }
    
    if (type === 'level' || type === 'price') {
      const index = newFilter[type].indexOf(value)
      if (index > -1) {
        newFilter[type].splice(index, 1)
      } else {
        newFilter[type].push(value)
      }
    } else {
      newFilter[type] = newFilter[type] === value ? '' : value
    }
    
    this.setData({
      filter: newFilter,
      page: 1,
      hasMore: true
    }, () => {
      this.loadCoachList()
    })
  },
  
  resetFilter: function() {
    this.setData({
      filter: {
        level: [],
        price: [],
        rating: null,
        gender: '',
        available: false
      },
      page: 1,
      hasMore: true
    }, () => {
      this.loadCoachList()
    })
  },
  
  // 收藏/取消收藏
  toggleFavorite: function(e) {
    const { id, index } = e.currentTarget.dataset
    const isFavorite = !this.data.list[index].isFavorite
    
    // 模拟API请求
    wx.showLoading({
      title: isFavorite ? '收藏中...' : '取消收藏...'
    })
    
    setTimeout(() => {
      this.setData({
        [`list[${index}].isFavorite`]: isFavorite
      })
      wx.hideLoading()
      wx.showToast({
        title: isFavorite ? '已收藏' : '已取消收藏',
        icon: 'success'
      })
    }, 500)
    
    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/coach/favorite`,
      method: 'POST',
      data: {
        coachId: id,
        favorite: isFavorite
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          this.setData({
            [`list[${index}].isFavorite`]: isFavorite
          })
          wx.showToast({
            title: isFavorite ? '已收藏' : '已取消收藏',
            icon: 'success'
          })
        }
      }
    })
    */
  },
  
  // 跳转到教练详情页
  goToDetail: function(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/coach/detail?id=${id}`
    })
  },
  
  // 分享
  onShareAppMessage: function () {
    return {
      title: '找台球教练，就上台球教练预约',
      path: '/pages/coach/list',
      imageUrl: '/images/share-image.png'
    }
  }
})