// pages/coach/list.js
const app = getApp()

Page({
  data: {
    // 页面状态
    loading: false,
    refreshing: false,
    loadingMore: false,
    hasMore: false,
    
    // 搜索关键词
    keyword: '',
    
    // 筛选条件
    filter: {
      province: '', // 省份
      level: '', // 级别
      category: '', // 种类
      gender: '' // 性别
    },
    
    // 下拉菜单状态
    showDropdown: '', // 当前显示的下拉菜单类型
    
    // 显示文本
    selectedProvinceText: '',
    selectedLevelText: '',
    selectedCategoryText: '',
    selectedGenderText: '',
    
    // 筛选选项
    provinceOptions: [
      // 直辖市
      { text: '北京', value: 'beijing' },
      { text: '上海', value: 'shanghai' },
      { text: '天津', value: 'tianjin' },
      { text: '重庆', value: 'chongqing' },
      // 省份
      { text: '河北', value: 'hebei' },
      { text: '山西', value: 'shanxi' },
      { text: '辽宁', value: 'liaoning' },
      { text: '吉林', value: 'jilin' },
      { text: '黑龙江', value: 'heilongjiang' },
      { text: '江苏', value: 'jiangsu' },
      { text: '浙江', value: 'zhejiang' },
      { text: '安徽', value: 'anhui' },
      { text: '福建', value: 'fujian' },
      { text: '江西', value: 'jiangxi' },
      { text: '山东', value: 'shandong' },
      { text: '河南', value: 'henan' },
      { text: '湖北', value: 'hubei' },
      { text: '湖南', value: 'hunan' },
      { text: '广东', value: 'guangdong' },
      { text: '海南', value: 'hainan' },
      { text: '四川', value: 'sichuan' },
      { text: '贵州', value: 'guizhou' },
      { text: '云南', value: 'yunnan' },
      { text: '陕西', value: 'shaanxi' },
      { text: '甘肃', value: 'gansu' },
      { text: '青海', value: 'qinghai' },
      { text: '台湾', value: 'taiwan' },
      // 自治区
      { text: '内蒙古', value: 'neimenggu' },
      { text: '广西', value: 'guangxi' },
      { text: '西藏', value: 'xizang' },
      { text: '宁夏', value: 'ningxia' },
      { text: '新疆', value: 'xinjiang' },
      // 特别行政区
      { text: '香港', value: 'hongkong' },
      { text: '澳门', value: 'macao' }
    ],
    levelOptions: [
      { text: '国家级', value: 'national' },
      { text: '高级', value: 'senior' },
      { text: '中级', value: 'intermediate' },
      { text: '初级', value: 'junior' }
    ],
    categoryOptions: [
      { text: '英式', value: 'english' },
      { text: '中式', value: 'chinese' },
      { text: '美式', value: 'american' }
    ],
    genderOptions: [
      { text: '男性', value: 'male' },
      { text: '女性', value: 'female' }
    ],
    
    // 列表数据
    page: 1,
    pageSize: 10,
    total: 10,
    list: [
      { id: 1, name: '教练1', avatar: '/images/default-avatar.png' },
      { id: 2, name: '教练2', avatar: '/images/default-avatar.png' },
      { id: 3, name: '教练3', avatar: '/images/default-avatar.png' },
      { id: 4, name: '教练4', avatar: '/images/default-avatar.png' },
      { id: 5, name: '教练5', avatar: '/images/default-avatar.png' },
      { id: 6, name: '教练6', avatar: '/images/default-avatar.png' },
      { id: 7, name: '教练7', avatar: '/images/default-avatar.png' },
      { id: 8, name: '教练8', avatar: '/images/default-avatar.png' },
      { id: 9, name: '教练9', avatar: '/images/default-avatar.png' },
      { id: 10, name: '教练10', avatar: '/images/default-avatar.png' }
    ]
  },

  onLoad: function (options) {
    // 处理分享或扫码进入的场景
    if (options.keyword) {
      this.setData({ keyword: options.keyword })
    }
    
    // 打印初始化狀態
    console.log('Page initialized with display texts:', {
      selectedProvinceText: this.data.selectedProvinceText,
      selectedLevelText: this.data.selectedLevelText,
      selectedCategoryText: this.data.selectedCategoryText,
      selectedGenderText: this.data.selectedGenderText
    })
    
    // 直接设置教练列表数据
    this.initCoachList()
  },

  onShow: function () {
    // 可能需要刷新列表（如收藏状态变化）
    if (this.data.list.length > 0) {
      this.updateCoachList()
    }
  },
  
  // 下拉刷新
  onPullDownRefresh: function () {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    }, () => {
      this.loadCoachList().then(() => {
        wx.stopPullDownRefresh()
        this.setData({ refreshing: false })
      })
    })
  },
  
  // 上拉加载更多（固定10位教练，无需加载更多）
  onReachBottom: function () {
    // 固定10位教练，不需要上拉加载更多
    return
  },
  
  // 初始化教练列表
  initCoachList: function() {
    // 固定显示10位教练
    const avatarMap = {
      1: '/images/coaches/coach1.jpg',
      2: '/images/coaches/coach2.jpg', 
      3: '/images/coaches/coach3.jpg',
      4: '/images/coaches/coach4.jpg',
      5: '/images/coaches/coach5.jpg',
      6: '/images/coaches/coach6.jpg',
      7: '/images/coaches/coach7.jpg',
      8: '/images/coaches/coach8.jpg',
      9: '/images/coaches/coach9.jpg',
      10: '/images/coaches/coach10.jpg'
    }
    
    // 级别标签配置
    const levelTags = [
      { text: '国家级', class: 'national' },
      { text: '高级教练', class: 'senior' },
      { text: '中级教练', class: 'intermediate' },
      { text: '初级教练', class: 'junior' }
    ]
    
    const mockList = Array(10).fill(0).map((_, index) => {
      const randomLevel = levelTags[Math.floor(Math.random() * 4)]
      return {
        id: index + 1,
        name: `教练${index + 1}`,
        avatar: avatarMap[index + 1],
        level: this.data.levelOptions[Math.floor(Math.random() * 4)].value,
        levelText: randomLevel.text,
        levelClass: randomLevel.class,
        gender: Math.random() > 0.5 ? 'male' : 'female',
        rating: (4 + Math.random()).toFixed(1),
        ratingCount: Math.floor(Math.random() * 1000),
        price: Math.floor(Math.random() * 400 + 100),
        tags: ['技术过硬', '耐心细致', '经验丰富'].slice(0, Math.floor(Math.random() * 3) + 1),
        available: Math.random() > 0.3,
        isFavorite: Math.random() > 0.7
      }
    })
    
    console.log('Coach list with avatars:', mockList.map(item => ({ id: item.id, name: item.name, avatar: item.avatar })))
    
    this.setData({
      loading: false,
      list: mockList,
      total: 10,
      hasMore: false
    })
  },
  
  // 加载教练列表
  loadCoachList: function (loadMore = false) {
    if (!loadMore) {
      this.setData({ loading: true })
    }
    
    // 构建请求参数
    const params = {
      page: this.data.page,
      pageSize: this.data.pageSize,
      keyword: this.data.keyword,
      ...this.data.filter
    }
    
    // 模拟API请求
    return new Promise((resolve) => {
      setTimeout(() => {
        // 固定显示10位教练
        const avatarMap = {
          1: '/images/coaches/coach1.jpg',
          2: '/images/coaches/coach2.jpg', 
          3: '/images/coaches/coach3.jpg',
          4: '/images/coaches/coach4.jpg',
          5: '/images/coaches/coach5.jpg',
          6: '/images/coaches/coach6.jpg',
          7: '/images/coaches/coach7.jpg',
          8: '/images/coaches/coach8.jpg',
          9: '/images/coaches/coach9.jpg',
          10: '/images/coaches/coach10.jpg'
        }
        
            // 级别标签配置
    const levelTags = [
      { text: '国家级', class: 'national' },
      { text: '高级教练', class: 'senior' },
      { text: '中级教练', class: 'intermediate' },
      { text: '初级教练', class: 'junior' }
    ]
    
    const mockList = Array(10).fill(0).map((_, index) => {
      const randomLevel = levelTags[Math.floor(Math.random() * 4)]
      return {
        id: index + 1,
        name: `教练${index + 1}`,
        avatar: avatarMap[index + 1],
        level: this.data.levelOptions[Math.floor(Math.random() * 4)].value,
        levelText: randomLevel.text,
        levelClass: randomLevel.class,
        gender: Math.random() > 0.5 ? 'male' : 'female',
        rating: (4 + Math.random()).toFixed(1),
        ratingCount: Math.floor(Math.random() * 1000),
        price: Math.floor(Math.random() * 400 + 100),
        tags: ['技术过硬', '耐心细致', '经验丰富'].slice(0, Math.floor(Math.random() * 3) + 1),
        available: Math.random() > 0.3,
        isFavorite: Math.random() > 0.7
      }
    })
        
        this.setData({
          loading: false,
          loadingMore: false,
          list: mockList,
          total: 10,
          hasMore: false
        })
        
        resolve()
      }, 1000)
    })
  },
  
  // 更新教练列表（如收藏状态变化）
  updateCoachList: function() {
    // 实际项目中，这里应该调用API获取最新的收藏状态
  },
  
    // 搜索相关方法
  onInputSearch: function(e) {
    this.setData({
      keyword: e.detail.value
    })
  },

  onSearch: function(e) {
    this.setData({
      keyword: e.detail,
      page: 1,
      hasMore: true
    }, () => {
      this.loadCoachList()
    })
  },

  onClear: function() {
    this.setData({
      keyword: '',
      page: 1,
      hasMore: true
    }, () => {
      this.loadCoachList()
    })
  },
  

  
  resetFilter: function() {
    console.log('Resetting all filters...')
    this.setData({
      filter: {
        province: '',
        level: '',
        category: '',
        gender: ''
      },
      selectedProvinceText: '', // 重置為空，確保顯示"省份"
      selectedLevelText: '',
      selectedCategoryText: '',
      selectedGenderText: '',
      showDropdown: '',
      page: 1,
      hasMore: false
    }, () => {
      console.log('Filters reset successfully - no initial highlighting')
      this.initCoachList()
    })
  },
  
  // 收藏/取消收藏
  toggleFavorite: function(e) {
    const { id, index } = e.currentTarget.dataset
    const isFavorite = !this.data.list[index].isFavorite
    
    // 模拟API请求
    wx.showLoading({
      title: isFavorite ? '收藏中...' : '取消收藏...'
    })
    
    setTimeout(() => {
      this.setData({
        [`list[${index}].isFavorite`]: isFavorite
      })
      wx.hideLoading()
      wx.showToast({
        title: isFavorite ? '已收藏' : '已取消收藏',
        icon: 'success'
      })
    }, 500)
  },
  
  // 跳转到教练详情页
  goToDetail: function(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/coach/detail?id=${id}`
    })
  },

  // 图片加载错误处理
  onImageError: function(e) {
    console.log('Image load error:', e.detail)
    console.log('Failed image src:', e.currentTarget.src)
    
    // 可以设置默认头像
    const src = e.currentTarget.src
    console.log('Setting default avatar for failed image:', src)
  },
  
  // 分享
  onShareAppMessage: function () {
    return {
      title: '找台球教练，就上台球教练预约',
      path: '/pages/coach/list',
      imageUrl: '/images/share-image.png'
    }
  },

  // 下拉菜单相关方法
  toggleDropdown: function(e) {
    const type = e.currentTarget.dataset.type;
    const currentDropdown = this.data.showDropdown;
    
    // 打印當前狀態用於調試
    console.log('Current filter display state:', {
      selectedProvinceText: this.data.selectedProvinceText,
      selectedLevelText: this.data.selectedLevelText,
      selectedCategoryText: this.data.selectedCategoryText,
      selectedGenderText: this.data.selectedGenderText,
      filter: this.data.filter
    })
    
    this.setData({
      showDropdown: currentDropdown === type ? '' : type
    })
  },

  // 隐藏下拉菜单
  hideDropdown: function() {
    this.setData({
      showDropdown: ''
    })
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止事件冒泡，防止点击下拉菜单时关闭
  },

  // 选择筛选项
  onFilterSelect: function(e) {
    const { type, value, text } = e.currentTarget.dataset
    let newFilter = { ...this.data.filter }
    
    // 设置筛选值
    newFilter[type] = value
    
    // 更新显示文本
    let updateData = {
      filter: newFilter,
      showDropdown: '',
      page: 1,
      hasMore: false
    }
    
    // 显示逻辑：
    // - 如果选择"不限"（value为空字符串），设置displayText为"不限"以便高亮显示
    // - 如果选择具体选项（value不为空），设置displayText为选择的文本
    // - 对於省份，需要特殊處理以控制高亮狀態
    switch(type) {
      case 'province':
        if (value === '' || value === null || value === undefined) {
          // 選擇"不限"時，設置特殊標記以便 WXML 判斷是否高亮
          updateData.selectedProvinceText = text // "不限"
        } else {
          // 選擇具體省份時，顯示省份名稱
          updateData.selectedProvinceText = text
        }
        console.log('Province selected:', { value, text, displayText: updateData.selectedProvinceText })
        break
      case 'level':
        if (value === '' || value === null || value === undefined) {
          // 選擇"不限"時，設置特殊標記以便 WXML 判斷是否高亮
          updateData.selectedLevelText = text // "不限"
        } else {
          // 選擇具體級別時，顯示級別名稱
          updateData.selectedLevelText = text
        }
        console.log('Level selected:', { value, text, displayText: updateData.selectedLevelText })
        break
      case 'category':
        if (value === '' || value === null || value === undefined) {
          // 選擇"不限"時，設置特殊標記以便 WXML 判斷是否高亮
          updateData.selectedCategoryText = text // "不限"
        } else {
          // 選擇具體種類時，顯示種類名稱
          updateData.selectedCategoryText = text
        }
        console.log('Category selected:', { value, text, displayText: updateData.selectedCategoryText })
        break
      case 'gender':
        if (value === '' || value === null || value === undefined) {
          // 選擇"不限"時，設置特殊標記以便 WXML 判斷是否高亮
          updateData.selectedGenderText = text // "不限"
        } else {
          // 選擇具體性別時，顯示性別名稱
          updateData.selectedGenderText = text
        }
        console.log('Gender selected:', { value, text, displayText: updateData.selectedGenderText })
        break
    }
    
    console.log('Filter update:', updateData)
    
    this.setData(updateData, () => {
      // 筛选后重新加载教练列表
      this.initCoachList()
    })
  },
});