/* pages/coach/list.wxss */

.container {
  min-height: 100vh;
  background-color: #f7fafc;
}

/* 搜索栏样式 */
.search-section {
  background: linear-gradient(135deg, #5a9178 0%, #4a7c64 100%);
  padding: 24rpx 32rpx 32rpx;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-wrapper {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.search-icon {
  font-size: 32rpx;
  color: #718096;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #2d3748;
  height: 60rpx;
  line-height: 60rpx;
}

.search-btn {
  color: #5a9178;
  font-size: 28rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background-color: rgba(90, 145, 120, 0.1);
}

.search-btn:active {
  transform: scale(0.95);
}

/* 筛选面板样式 */
.filter-panel {
  background-color: #ffffff;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  min-width: 120rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  flex: 1;
  margin-left: 24rpx;
}

.filter-option {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: #edf2f7;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #4a5568;
  border: 2rpx solid transparent;
}

.filter-option.active {
  background-color: rgba(90, 145, 120, 0.1);
  color: #5a9178;
  border-color: #5a9178;
}

.filter-option:active {
  transform: scale(0.95);
}

/* 排序栏样式 */
.sort-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx 16rpx;
  background-color: #f7fafc;
}

.sort-label {
  font-size: 26rpx;
  color: #718096;
}

.sort-options {
  display: flex;
  gap: 24rpx;
}

.sort-option {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #4a5568;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}

.sort-option.active {
  color: #5a9178;
  font-weight: 500;
  background-color: rgba(90, 145, 120, 0.1);
}

.sort-icon {
  margin-left: 8rpx;
  font-size: 20rpx;
}

/* 教练列表样式 */
.coach-list {
  padding: 0 32rpx 32rpx;
}

.coach-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1rpx solid #e2e8f0;
}

.coach-item:active {
  transform: scale(0.98);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 教练头部信息 */
.coach-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #e2e8f0;
}

.coach-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
  background-color: #edf2f7;
  border: 3rpx solid #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.coach-info {
  flex: 1;
}

.coach-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.coach-title {
  font-size: 26rpx;
  color: #5a9178;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.coach-rating {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.rating-stars {
  display: flex;
  margin-right: 8rpx;
}

.star {
  font-size: 24rpx;
  color: #ddd;
  margin-right: 2rpx;
}

.star.filled {
  color: #ffd700;
}

.rating-text {
  font-size: 24rpx;
  color: #718096;
}

.coach-price {
  display: flex;
  align-items: baseline;
  margin-top: 8rpx;
}

.price-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b6b;
  margin-right: 8rpx;
}

.price-unit {
  font-size: 24rpx;
  color: #718096;
}

/* 教练身体信息 */
.coach-body {
  padding: 24rpx 32rpx;
}

.coach-specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.specialty-tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: rgba(90, 145, 120, 0.1);
  color: #5a9178;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: 1rpx solid rgba(90, 145, 120, 0.2);
}

.coach-description {
  font-size: 26rpx;
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 24rpx;
}

.coach-stats {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 0;
  border-top: 1rpx solid #e2e8f0;
  border-bottom: 1rpx solid #e2e8f0;
  margin-bottom: 24rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #718096;
}

/* 教练底部操作 */
.coach-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #f7fafc;
}

.coach-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background-color: #51cf66;
  margin-right: 8rpx;
}

.status-indicator.busy {
  background-color: #ff6b6b;
}

.status-text {
  font-size: 24rpx;
  color: #4a5568;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.contact-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: #ffffff;
  color: #5a9178;
  border: 2rpx solid #5a9178;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.contact-btn:active {
  background-color: #5a9178;
  color: white;
  transform: scale(0.95);
}

.book-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: #5a9178;
  color: white;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.book-btn:active {
  background-color: #4a7c64;
  transform: scale(0.95);
}

/* 加载更多样式 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
  color: #718096;
  font-size: 26rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #e2e8f0;
  border-top: 3rpx solid #5a9178;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64rpx 32rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #718096;
  line-height: 1.5;
}