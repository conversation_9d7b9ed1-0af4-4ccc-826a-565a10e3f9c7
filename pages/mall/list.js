// pages/mall/list.js
const app = getApp()

Page({
  data: {
    goodsList: [], // 商品數據
    loading: false,
    searchKeyword: '',
    total: 10, // 總商品數
  },

  onLoad() {
    console.log('商城頁面加載')
    this.getGoodsList()
  },

  onShow() {
    console.log('商城頁面顯示')
  },

  // 模擬獲取商品數據
  getGoodsList() {
    this.setData({ loading: true })
    setTimeout(() => {
      const mockGoodsList = Array(10).fill(0).map((_, index) => ({
        id: index + 1,
        title: `商品${index + 1}`,
        image: `/images/goods/${String.fromCharCode(65 + index)}/main.jpg`, // A, B, C...J
        price: Math.floor(Math.random() * 400 + 100), // 隨機價格100-500
        originalPrice: Math.floor(Math.random() * 200 + 500), // 隨機原價500-700
        soldCount: Math.floor(Math.random() * 200 + 10) // 隨機銷量10-210
      }))
      
      this.setData({
        goodsList: mockGoodsList,
        loading: false
      })
    }, 500)
  },

  // 搜索
  onInputSearch(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  onSearch() {
    console.log('搜索關鍵詞：', this.data.searchKeyword)
  },

  onClearSearch() {
    this.setData({
      searchKeyword: ''
    })
  },

  // 商品詳情
  goToGoodsDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/mall/detail?id=${id}`
    })
  },

  // 加入購物車
  addToCart(e) {
    const id = e.currentTarget.dataset.id
    console.log('添加到購物車：', id)
    wx.showToast({
      title: '已加入購物車',
      icon: 'success'
    })
  },

  // 圖片加載/錯誤
  onImageLoad() {},
  onImageError(e) {
    // 可設置默認圖片
  }
})