<!--pages/mall/list.wxml-->
<view class="container">
  <view class="search-container">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索商品" 
        value="{{searchKeyword}}"
        bindinput="onInputSearch"
        bindconfirm="onSearch"
        confirm-type="search"
      />
      <text wx:if="{{searchKeyword}}" class="clear-icon" bindtap="onClearSearch">✖</text>
    </view>
  </view>
  <view class="section">
    <view class="goods-waterfall">
      <block wx:if="{{goodsList.length === 0}}">
        <view class="empty-tip">暂无商品信息</view>
      </block>
      <block wx:else>
        <!-- 左列 -->
        <view class="goods-column">
          <view 
            class="goods-item" 
            wx:for="{{goodsList}}" 
            wx:key="id" 
            wx:if="{{index % 2 === 0}}"
            bindtap="goToGoodsDetail" 
            data-id="{{item.id}}"
          >
            <image 
              class="goods-image" 
              src="{{item.image}}" 
              mode="widthFix"
              binderror="onImageError"
              bindload="onImageLoad"
              data-id="{{item.id}}"
              lazy-load="true"
            ></image>
            <view class="goods-info">
              <text class="goods-title">{{item.title}}</text>
              <view class="goods-price-row">
                <view class="goods-price">
                  <text class="price-symbol">¥</text>
                  <text class="price-value">{{item.price}}</text>
                  <text class="price-original">¥{{item.originalPrice}}</text>
                </view>
              </view>
              <view class="goods-bottom">
                <text class="goods-sold">已售 {{item.soldCount}}</text>
                <view class="goods-cart" catchtap="addToCart" data-id="{{item.id}}">
                  <text class="cart-icon">🛒</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 右列 -->
        <view class="goods-column">
          <view 
            class="goods-item" 
            wx:for="{{goodsList}}" 
            wx:key="id" 
            wx:if="{{index % 2 === 1}}"
            bindtap="goToGoodsDetail" 
            data-id="{{item.id}}"
          >
            <image 
              class="goods-image" 
              src="{{item.image}}" 
              mode="widthFix"
              binderror="onImageError"
              bindload="onImageLoad"
              data-id="{{item.id}}"
              lazy-load="true"
            ></image>
            <view class="goods-info">
              <text class="goods-title">{{item.title}}</text>
              <view class="goods-price-row">
                <view class="goods-price">
                  <text class="price-symbol">¥</text>
                  <text class="price-value">{{item.price}}</text>
                  <text class="price-original">¥{{item.originalPrice}}</text>
                </view>
              </view>
              <view class="goods-bottom">
                <text class="goods-sold">已售 {{item.soldCount}}</text>
                <view class="goods-cart" catchtap="addToCart" data-id="{{item.id}}">
                  <text class="cart-icon">🛒</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
  <view class="safe-bottom"></view>
</view>