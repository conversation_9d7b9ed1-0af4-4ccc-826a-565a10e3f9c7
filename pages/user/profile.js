// pages/user/profile.js
const app = getApp()

Page({
  data: {
    // 用户信息
    userInfo: null,
    // 表单数据
    formData: {
      nickName: '',
      gender: 1, // 1-男性，2-女性，0-未知
      phone: '',
      email: '',
      birthday: '',
      introduction: ''
    },
    // 性别选项
    genderOptions: [
      { id: 1, name: '男' },
      { id: 2, name: '女' },
      { id: 0, name: '不显示' }
    ],
    // 是否正在提交
    isSubmitting: false
  },

  onLoad: function (options) {
    // 获取用户信息
    this.getUserInfo()
  },

  // 获取用户信息
  getUserInfo: function () {
    // 从全局获取用户信息
    const userInfo = app.globalData.userInfo
    
    if (userInfo) {
      // 设置表单初始数据
      this.setData({
        userInfo: userInfo,
        formData: {
          nickName: userInfo.nickName || '',
          gender: userInfo.gender || 1,
          phone: userInfo.phone || '',
          email: userInfo.email || '',
          birthday: userInfo.birthday || '',
          introduction: userInfo.introduction || ''
        }
      })
    } else {
      // 未登录，跳转到登录页面
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/index'
        })
      }, 1500)
    }
  },

  // 表单输入事件处理
  onInput: function (e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    // 更新表单数据
    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 选择性别
  onGenderChange: function (e) {
    this.setData({
      'formData.gender': parseInt(e.detail.value)
    })
  },

  // 选择生日
  onBirthdayChange: function (e) {
    this.setData({
      'formData.birthday': e.detail.value
    })
  },

  // 选择头像
  chooseAvatar: function () {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 获取选择的图片临时路径
        const tempFilePath = res.tempFilePaths[0]
        
        // 上传头像
        this.uploadAvatar(tempFilePath)
      }
    })
  },

  // 上传头像
  uploadAvatar: function (filePath) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    })
    
    // 模拟上传过程
    setTimeout(() => {
      // 实际应用中，这里应该调用上传API
      // 上传成功后更新用户信息
      const userInfo = this.data.userInfo
      userInfo.avatarUrl = filePath
      
      this.setData({
        userInfo: userInfo
      })
      
      wx.hideLoading()
      
      wx.showToast({
        title: '上传成功',
        icon: 'success'
      })
    }, 1000)
  },

  // 提交表单
  submitForm: function () {
    // 表单验证
    const { nickName } = this.data.formData
    
    if (!nickName) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }
    
    // 防止重复提交
    if (this.data.isSubmitting) {
      return
    }
    
    this.setData({
      isSubmitting: true
    })
    
    wx.showLoading({
      title: '保存中...',
      mask: true
    })
    
    // 模拟保存过程
    setTimeout(() => {
      // 实际应用中，这里应该调用API保存用户信息
      
      // 更新全局用户信息
      const userInfo = {
        ...app.globalData.userInfo,
        ...this.data.formData,
        avatarUrl: this.data.userInfo.avatarUrl
      }
      
      app.globalData.userInfo = userInfo
      
      wx.hideLoading()
      
      this.setData({
        isSubmitting: false
      })
      
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }, 1000)
  }
})