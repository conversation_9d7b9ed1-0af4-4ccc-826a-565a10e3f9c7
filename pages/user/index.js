// pages/user/index.js
const app = getApp()

Page({
  data: {
    userInfo: {
      avatarUrl: '/images/default-avatar.png',
      nickName: '未登录'
    },
    isLogin: false,
    userType: '', // 'student' 或 'coach'
    functionList: [],
    settingList: [
      {
        id: 1,
        name: '个人资料',
        icon: '/images/icon/profile.png',
        url: '/pages/user/profile'
      },
      {
        id: 2,
        name: '消息通知',
        icon: '/images/icon/notification.png',
        url: '/pages/notification/index'
      },
      {
        id: 3,
        name: '隐私政策',
        icon: '/images/icon/privacy.png',
        url: '/pages/agreement/privacy'
      },
      {
        id: 4,
        name: '用户协议',
        icon: '/images/icon/agreement.png',
        url: '/pages/agreement/user'
      }
    ]
  },

  onLoad: function () {
    console.log('User page loaded.');
    this.checkLoginStatus();
    
    // 确保有默认的功能列表和设置列表
    this.ensureDefaultLists();
  },

  onShow: function() {
    console.log('User page showed.');
    this.checkLoginStatus();
    
    // 确保有默认的功能列表和设置列表
    this.ensureDefaultLists();
    
    if (this.data.isLogin) {
      this.updateFunctionList();
    }
  },
  
  // 确保有默认的功能列表和设置列表
  ensureDefaultLists: function() {
    // 如果功能列表为空，设置默认功能
    if (!this.data.functionList || this.data.functionList.length === 0) {
      this.setData({
        functionList: [
          {
            id: 1,
            name: '我的预约',
            icon: '/images/icon/cart.png',
            url: '/pages/appointment/my-appointments'
          },
          {
            id: 2,
            name: '我的课程',
            icon: '/images/icon/cart.png',
            url: '/pages/course/my-courses'
          }
        ]
      });
    }
    
    // 确保设置列表有内容
    if (!this.data.settingList || this.data.settingList.length === 0) {
      this.setData({
        settingList: [
          {
            id: 1,
            name: '个人资料',
            icon: '/images/icon/cart.png',
            url: '/pages/user/profile'
          },
          {
            id: 2,
            name: '消息通知',
            icon: '/images/icon/cart.png',
            url: '/pages/notification/index'
          }
        ]
      });
    }
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const app = getApp();
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    const userType = wx.getStorageSync('userType');

    console.log('检查登录状态:', token, userInfo, userType);

    if (token && userInfo) {
      // 确保用户类型的一致性
      const finalUserType = userInfo.userType || userType || 'student';
      
      this.setData({
        isLogin: true,
        userInfo: {
          ...userInfo,
          userType: finalUserType // 确保用户信息中包含类型
        },
        userType: finalUserType
      });
      
      // 同步全局数据
      app.globalData.userInfo = this.data.userInfo;
      app.globalData.userType = finalUserType;
      app.globalData.isLogin = true;
      
      this.updateFunctionList();
    } else {
      this.setData({
        isLogin: false,
        userInfo: {
          avatarUrl: '/images/default-avatar.png',
          nickName: '未登录'
        },
        userType: ''
      });
      
      // 清空全局数据
      app.globalData.userInfo = null;
      app.globalData.userType = '';
      app.globalData.isLogin = false;
    }
    
    // 如果未登录，清空功能列表
    if (!this.data.isLogin) {
      this.setData({
        functionList: []
      });
    }
  },

  // 根据用户类型更新功能列表
  updateFunctionList: function() {
    let functionList = [];
    
    if (this.data.userType === 'student') {
      functionList = [
        {
          id: 1,
          name: '我的预约',
          icon: '/images/icon/appointment.png',
          url: '/pages/appointment/my-appointments'
        },
        {
          id: 2,
          name: '我的课程',
          icon: '/images/icon/course.png',
          url: '/pages/course/my-courses'
        },
        {
          id: 3,
          name: '我的订单',
          icon: '/images/icon/order.png',
          url: '/pages/order/list'
        },
        {
          id: 4,
          name: '意见反馈',
          icon: '/images/icon/feedback.png',
          url: '/pages/feedback/index'
        }
      ];
    } else if (this.data.userType === 'coach') {
      functionList = [
        {
          id: 1,
          name: '预约管理',
          icon: '/images/icon/appointment-manage.png',
          url: '/pages/coach/appointments'
        },
        {
          id: 2,
          name: '课程管理',
          icon: '/images/icon/course-manage.png',
          url: '/pages/coach/courses'
        },
        {
          id: 3,
          name: '学员管理',
          icon: '/images/icon/student-manage.png',
          url: '/pages/coach/students'
        },
        {
          id: 4,
          name: '收入统计',
          icon: '/images/icon/income.png',
          url: '/pages/coach/income'
        }
      ];
    }

    this.setData({ functionList });
  },

  // 跳转到登录页
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 跳转到注册页
  goToRegister: function() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  },

  // 页面导航
  navigateTo: function(e) {
    const url = e.currentTarget.dataset.url;
    wx.navigateTo({ url });
  },

  // 退出登录
  logout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          wx.removeStorageSync('userType');
          
          // 更新页面状态
          this.setData({
            isLogin: false,
            userInfo: {
              avatarUrl: '/images/default-avatar.png',
              nickName: '未登录'
            },
            userType: '',
            functionList: []
          });
          
          // 显示提示
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  }
});