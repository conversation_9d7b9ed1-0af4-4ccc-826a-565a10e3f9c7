<!--pages/user/index.wxml-->
<view class="container">


  <!-- 用户信息区域 -->
  <view class="user-info-section">
    <view class="user-info-wrapper" wx:if="{{isLogin}}">
      <view class="avatar-wrapper">
        <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"></image>
      </view>
      <view class="user-detail">
        <view class="nickname-row">
          <text class="nickname">{{userInfo.nickName || '用户'}}</text>
          <view class="user-type-tag {{userType === 'coach' ? 'coach-tag' : 'student-tag'}}">
            {{userType === 'coach' ? '教练' : '学员'}}
          </view>
        </view>
        <view class="phone">{{userInfo.phone || '未绑定手机号'}}</view>
      </view>
      <view class="edit-profile" bindtap="navigateTo" data-url="/pages/user/profile">
        <text class="edit-text">编辑</text>
        <image class="arrow-icon" src="/images/icon/arrow-right.png"></image>
      </view>
    </view>
    
    <!-- 未登录状态 -->
    <view class="not-login-wrapper" wx:else>
      <view class="avatar-wrapper">
        <image class="avatar" src="/images/default-avatar.png"></image>
      </view>
      <view class="guest-text">
        <view class="welcome-text">欢迎来到Coach-Q</view>
        <view class="login-tip">登录或注册体验完整功能</view>
      </view>
      <view class="auth-buttons">
        <button class="login-btn" bindtap="goToLogin">登录</button>
        <button class="register-btn" bindtap="goToRegister">注册</button>
      </view>
    </view>
  </view>
  
  <!-- 功能列表区域 -->
  <view class="section-title" wx:if="{{isLogin}}">我的服务</view>
  <view class="function-section" wx:if="{{isLogin}}">
    <view class="function-list">
      <view class="function-item" wx:for="{{functionList}}" wx:key="id" bindtap="navigateTo" data-url="{{item.url}}">
        <image class="function-icon" src="{{item.icon}}"></image>
        <view class="function-name">{{item.name}}</view>
      </view>
    </view>
  </view>
  

  
  <!-- 设置列表区域 -->
  <view class="section-title">设置</view>
  <view class="setting-section">
    <view class="setting-list">
      <view class="setting-item" wx:for="{{settingList}}" wx:key="id" bindtap="navigateTo" data-url="{{item.url}}">
        <view class="setting-left">
          <image class="setting-icon" src="{{item.icon}}"></image>
          <view class="setting-name">{{item.name}}</view>
        </view>
        <image class="arrow-icon" src="/images/icon/arrow-right.png"></image>
      </view>
    </view>
  </view>
  
  <!-- 退出登录按钮 -->
  <view class="logout-section" wx:if="{{isLogin}}">
    <button class="logout-button" bindtap="logout">退出登录</button>
  </view>
  
  <!-- 底部安全区域 -->
  <view class="safe-bottom"></view>
</view>