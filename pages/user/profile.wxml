<!--pages/user/profile.wxml-->
<view class="container">
  <!-- 头像区域 -->
  <view class="avatar-section">
    <view class="avatar-wrapper" bindtap="chooseAvatar">
      <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"></image>
      <view class="avatar-edit">
        <image class="edit-icon" src="/images/icon/camera.png"></image>
      </view>
    </view>
    <view class="avatar-tip">点击更换头像</view>
  </view>

  <!-- 表单区域 -->
  <view class="form-section">
    <!-- 基本信息 -->
    <view class="form-group">
      <view class="form-item">
        <view class="form-label">昵称</view>
        <input class="form-input" 
          type="text"
          placeholder="请输入昵称"
          value="{{formData.nickName}}"
          data-field="nickName"
          bindinput="onInput"
        />
      </view>

      <view class="form-item">
        <view class="form-label">性别</view>
        <picker class="form-picker" 
          mode="selector" 
          range="{{genderOptions}}" 
          range-key="name"
          value="{{formData.gender}}"
          bindchange="onGenderChange"
        >
          <view class="picker-text">{{genderOptions[formData.gender].name}}</view>
          <image class="arrow-icon" src="/images/icon/arrow-right.png"></image>
        </picker>
      </view>

      <view class="form-item">
        <view class="form-label">手机号</view>
        <input class="form-input" 
          type="number"
          placeholder="请输入手机号"
          value="{{formData.phone}}"
          data-field="phone"
          bindinput="onInput"
          maxlength="11"
        />
      </view>

      <view class="form-item">
        <view class="form-label">邮箱</view>
        <input class="form-input" 
          type="text"
          placeholder="请输入邮箱"
          value="{{formData.email}}"
          data-field="email"
          bindinput="onInput"
        />
      </view>

      <view class="form-item">
        <view class="form-label">生日</view>
        <picker class="form-picker" 
          mode="date" 
          value="{{formData.birthday}}"
          bindchange="onBirthdayChange"
        >
          <view class="picker-text">{{formData.birthday || '请选择生日'}}</view>
          <image class="arrow-icon" src="/images/icon/arrow-right.png"></image>
        </picker>
      </view>
    </view>

    <!-- 个人简介 -->
    <view class="form-group">
      <view class="form-title">个人简介</view>
      <textarea class="form-textarea"
        placeholder="介绍一下自己吧..."
        value="{{formData.introduction}}"
        data-field="introduction"
        bindinput="onInput"
        maxlength="200"
      ></textarea>
      <view class="textarea-counter">{{formData.introduction.length}}/200</view>
    </view>
  </view>

  <!-- 保存按钮 -->
  <view class="button-section">
    <button class="save-button {{isSubmitting ? 'disabled' : ''}}" 
      bindtap="submitForm"
      disabled="{{isSubmitting}}"
    >
      保存
    </button>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-bottom"></view>
</view>