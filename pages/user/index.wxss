/* pages/user/index.wxss */

.container {
  min-height: 100vh;
  background-color: #f7fafc;
  padding: 0 0 32rpx 0;
  box-sizing: border-box;
}

/* 用户信息区域样式 */
.user-info-section {
  background: linear-gradient(135deg, #5a9178 0%, #4a7c64 100%);
  padding: 48rpx 32rpx;
  margin-bottom: 0;
  position: relative;
  overflow: hidden;
}

.user-info-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
  opacity: 0.3;
}

.user-info-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.not-login-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  z-index: 2;
}

.user-info-wrapper .avatar-wrapper {
  position: relative;
  margin-right: 32rpx;
}

.not-login-wrapper .avatar-wrapper {
  position: relative;
  margin-bottom: 24rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-detail {
  flex: 1;
}

.nickname-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-right: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.user-type-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  color: white;
  font-weight: 500;
}

.student-tag {
  background-color: #4299e1;
}

.coach-tag {
  background-color: #ed8936;
}

.phone {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.edit-profile {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.edit-text {
  font-size: 24rpx;
  color: white;
  margin-right: 8rpx;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  color: white;
}

/* 未登录状态样式 */
.login-tip {
  flex: 1;
}

.login-text {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.login-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20rpx;
}

/* 客人文字区域样式 */
.not-login-wrapper .guest-text {
  margin-bottom: 32rpx;
}

.welcome-text {
  font-size: 36rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.not-login-wrapper .login-tip {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 登录注册按钮样式 */
.auth-buttons {
  display: flex;
  gap: 16rpx;
  width: 100%;
  max-width: 480rpx;
}

.login-btn, .register-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 40rpx;
  border: none;
  padding: 0;
  margin: 0;
}

.login-btn::after, .register-btn::after {
  border: none;
}

.login-btn {
  background-color: white;
  color: #5a9178;
}

.register-btn {
  background-color: transparent;
  color: white;
  border: 2rpx solid white;
}

.login-btn:active {
  background-color: rgba(255, 255, 255, 0.8);
}

.register-btn:active {
  background-color: rgba(255, 255, 255, 0.1);
}



/* 功能列表区域样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  padding: 32rpx 32rpx 24rpx;
}

.function-section {
  background-color: #ffffff;
  border-radius: 0;
  margin: 0;
  box-shadow: none;
  overflow: hidden;
}

.function-list {
  display: flex;
  flex-wrap: nowrap;
  padding: 16rpx;
}

.function-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  position: relative;
}

.function-item:active {
  background-color: #f7fafc;
  transform: scale(0.95);
}

.function-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 16rpx;
  background-color: #5a9178;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
}

.function-name {
  font-size: 26rpx;
  color: #4a5568;
  font-weight: 500;
}

/* 设置列表区域样式 */
.setting-section {
  background-color: #ffffff;
  border-radius: 0;
  margin: 0;
  box-shadow: none;
  overflow: hidden;
}

.setting-list {
  padding: 0;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #e2e8f0;
  transition: all 0.2s ease;
  position: relative;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #f7fafc;
}

.setting-left {
  display: flex;
  align-items: center;
}

.setting-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
  background-color: #5a9178;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
}

.setting-name {
  font-size: 30rpx;
  color: #2d3748;
  font-weight: 500;
}

.setting-right {
  display: flex;
  align-items: center;
  color: #718096;
}

.setting-value {
  font-size: 26rpx;
  margin-right: 8rpx;
}

.setting-arrow {
  font-size: 24rpx;
  color: #a0aec0;
}

/* 退出登录按钮样式 */
.logout-section {
  padding: 48rpx 0;
  background-color: #ffffff;
}

.logout-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #ffffff;
  color: #ff6b6b;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 0;
  border: none;
  border-top: 2rpx solid #ff6b6b;
  box-shadow: none;
  transition: all 0.2s ease;
}

.logout-button::after {
  border: none;
}

.logout-button:active {
  background-color: #ff6b6b;
  color: white;
  transform: scale(0.98);
}

/* 点击效果 */
.function-item:active,
.setting-item:active {
  opacity: 0.8;
}

/* 底部安全区域 */
.safe-bottom {
  height: env(safe-area-inset-bottom);
  background-color: #f7fafc;
}

.footer {
  text-align: center;
  width: 100%;
}

.help-text {
  display: inline;
}

.help-link {
  display: inline-block;
}