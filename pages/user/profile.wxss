/* pages/user/profile.wxss */

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0;
}

/* 头像区域样式 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.avatar-wrapper {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 80rpx;
  background-color: #f0f0f0;
}

.avatar-edit {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 48rpx;
  height: 48rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.edit-icon {
  width: 32rpx;
  height: 32rpx;
}

.avatar-tip {
  font-size: 28rpx;
  color: #999999;
}

/* 表单区域样式 */
.form-section {
  background-color: #ffffff;
}

.form-group {
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.form-title {
  font-size: 32rpx;
  color: #333333;
  padding: 30rpx 0 20rpx;
  font-weight: 500;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 160rpx;
  font-size: 32rpx;
  color: #333333;
}

.form-input {
  flex: 1;
  height: 100rpx;
  font-size: 32rpx;
  color: #333333;
}

.form-picker {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.picker-text {
  font-size: 32rpx;
  color: #333333;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 文本域样式 */
.form-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx 0;
  font-size: 32rpx;
  line-height: 1.5;
  color: #333333;
}

.textarea-counter {
  text-align: right;
  font-size: 28rpx;
  color: #999999;
  padding: 10rpx 0;
}

/* 按钮区域样式 */
.button-section {
  padding: 40rpx 30rpx;
}

.save-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #07c160;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
}

.save-button::after {
  border: none;
}

.save-button.disabled {
  opacity: 0.6;
}

/* 输入框placeholder样式 */
.form-input::placeholder,
.form-textarea::placeholder {
  color: #999999;
}

/* 底部安全区域 */
.safe-bottom {
  height: env(safe-area-inset-bottom);
  background-color: #f5f5f5;
}

/* 点击效果 */
.avatar-wrapper:active {
  opacity: 0.7;
}

.save-button:active {
  opacity: 0.8;
}