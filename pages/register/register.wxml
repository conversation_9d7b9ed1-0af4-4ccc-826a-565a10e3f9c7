<!--pages/register/register.wxml-->
<view class="container">
  
  <!-- 用户类型选择 -->
  <view class="user-type-selector">
    <view 
      class="type-item {{userType === 'student' ? 'active' : ''}}" 
      bindtap="switchUserType" 
      data-type="student"
    >
      <image class="type-icon" src="/images/student-icon.png"></image>
      <text class="type-text">我是学员</text>
    </view>
    <view 
      class="type-item {{userType === 'coach' ? 'active' : ''}}" 
      bindtap="switchUserType" 
      data-type="coach"
    >
      <image class="type-icon" src="/images/coach-icon.png"></image>
      <text class="type-text">我是教练</text>
    </view>
  </view>
  
  <view class="register-form">
    <!-- 手机号输入 -->
    <view class="form-item">
      <view class="input-container">
        <image class="input-icon" src="/images/icon-phone.png"></image>
        <input 
          class="input" 
          type="number" 
          placeholder="请输入手机号" 
          maxlength="11" 
          value="{{phone}}" 
          bindinput="inputPhone"
        />
      </view>
    </view>
    
    <!-- 验证码输入 -->
    <view class="form-item">
      <view class="input-container">
        <image class="input-icon" src="/images/icon-verify.png"></image>
        <input 
          class="input" 
          type="number" 
          placeholder="请输入验证码" 
          maxlength="6" 
          value="{{verifyCode}}" 
          bindinput="inputVerifyCode"
        />
        <view 
          class="verify-btn {{countdown > 0 ? 'disabled' : ''}}" 
          bindtap="getVerifyCode"
        >
          {{countdown > 0 ? countdown + 's' : '获取验证码'}}
        </view>
      </view>
    </view>
    
    <!-- 密码输入 -->
    <view class="form-item">
      <view class="input-container">
        <image class="input-icon" src="/images/icon-password.png"></image>
        <input 
          class="input" 
          password="{{!showPassword}}" 
          placeholder="请设置密码" 
          value="{{password}}" 
          bindinput="inputPassword"
        />
        <view class="password-toggle" bindtap="togglePasswordVisibility">
          <image class="eye-icon" src="{{showPassword ? '/images/eye-open.png' : '/images/eye-close.png'}}"></image>
        </view>
      </view>
    </view>
    
    <!-- 确认密码输入 -->
    <view class="form-item">
      <view class="input-container">
        <image class="input-icon" src="/images/icon-password.png"></image>
        <input 
          class="input" 
          password="{{!showConfirmPassword}}" 
          placeholder="请确认密码" 
          value="{{confirmPassword}}" 
          bindinput="inputConfirmPassword"
        />
        <view class="password-toggle" bindtap="toggleConfirmPasswordVisibility">
          <image class="eye-icon" src="{{showConfirmPassword ? '/images/eye-open.png' : '/images/eye-close.png'}}"></image>
        </view>
      </view>
    </view>
    
    <!-- 错误信息 -->
    <view class="error-msg" wx:if="{{errorMsg}}">{{errorMsg}}</view>
    
    <!-- 协议勾选 -->
    <view class="agreement-row">
      <view class="checkbox-container" bindtap="toggleAgreement">
        <view class="checkbox {{isAgreement ? 'checked' : ''}}">
          <icon wx:if="{{isAgreement}}" type="success" size="12" color="#3cc51f"></icon>
        </view>
      </view>
      <view class="agreement-text">
        我已阅读并同意
        <text class="link" bindtap="goToAgreement">《用户协议》</text>
        和
        <text class="link" bindtap="goToPrivacy">《隐私政策》</text>
      </view>
    </view>
    
    <!-- 注册按钮 -->
    <button 
      class="register-btn {{isLoading ? 'loading' : ''}}" 
      bindtap="handleRegister" 
      disabled="{{isLoading}}"
    >
      {{isLoading ? '注册中...' : '注册'}}
    </button>
    
    <!-- 登录链接 -->
    <view class="login-link">
      已有账号？<text class="link" bindtap="goToLogin">立即登录</text>
    </view>
  </view>
  
  <!-- 教练注册说明 -->
  <view class="coach-tips" wx:if="{{userType === 'coach'}}">
    <view class="tips-title">
      <image class="tips-icon" src="/images/icon-tips.png"></image>
      <text>教练注册须知</text>
    </view>
    <view class="tips-content">
      <text>1. 注册成为教练后，需要完善个人资料和上传相关证书</text>
      <text>2. 资料审核通过后，您的教练信息将展示在平台上</text>
      <text>3. 您可以发布课程、接受预约并获得相应收益</text>
      <text>4. 如有疑问，请联系客服：400-123-4567</text>
    </view>
  </view>
</view>