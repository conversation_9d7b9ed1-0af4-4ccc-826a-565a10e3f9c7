/* pages/register/register.wxss */

.container {
  padding: 40rpx 25rpx;
  min-height: 100vh;
  box-sizing: border-box;
  background: #f7f9fc;
  position: relative;
}

/* 用户类型选择器 */
.user-type-selector {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
  gap: 24rpx;
  position: relative;
  z-index: 2;
  padding: 0;
}

/* 移除分隔線以符合扁平化設計 */

.type-item {
  flex: 1;
  max-width: 280rpx;
  height: 96rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  padding: 0 20rpx;
  cursor: pointer;
  overflow: hidden;
}

/* 學員按鈕樣式 - 偏藍的綠色系 */
.type-item[data-type="student"] {
  background-color: #e6f3f7;
  color: #047857;
}

/* 教練按鈕樣式 - 偏暖的綠色系 */
.type-item[data-type="coach"] {
  background-color: #ecf7ed;
  color: #059669;
}

/* 學員激活狀態 */
.type-item[data-type="student"].active {
  background-color: #0f766e;
  color: white;
  transform: scale(1.02);
}

/* 教練激活狀態 */
.type-item[data-type="coach"].active {
  background-color: #16a34a;
  color: white;
  transform: scale(1.02);
}

/* 按壓效果 */
.type-item:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 學員懸停效果 */
.type-item[data-type="student"]:hover {
  background-color: #ccfbf1;
  transform: scale(1.01);
}

/* 教練懸停效果 */
.type-item[data-type="coach"]:hover {
  background-color: #dcfce7;
  transform: scale(1.01);
}

.type-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.type-text {
  font-size: 30rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  line-height: 1.2;
  white-space: nowrap;
  position: relative;
  z-index: 1;
  letter-spacing: 0.5rpx;
}

/* 激活狀態文字樣式由父元素的 color 控制 */
.type-item.active .type-text {
  font-weight: 700;
}

/* 激活狀態圖標樣式 */
.type-item.active .type-icon {
  transform: scale(1.1);
  filter: brightness(1.2);
}

/* 注册表单 */
.register-form {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 32rpx 25rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 2;
}

.form-item {
  margin-bottom: 24rpx;
}

.input-container {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #f8f9fa;
  border-radius: 14rpx;
  padding: 0 16rpx;
  position: relative;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.input-container:focus-within {
  background-color: white;
  border-color: #5a9178;
  box-shadow: 0 0 0 6rpx rgba(90, 145, 120, 0.1);
}

.input-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.input {
  flex: 1;
  height: 88rpx;
  font-size: 28rpx;
  color: #2d3748;
  background-color: transparent;
  border: none;
}

.input::placeholder {
  color: #a0aec0;
}

.verify-btn {
  padding: 0 16rpx;
  height: 56rpx;
  line-height: 56rpx;
  background-color: #5a9178;
  color: white;
  font-size: 24rpx;
  font-weight: 600;
  border-radius: 28rpx;
  transition: all 0.3s ease;
  border: none;
  margin-left: 8rpx;
  white-space: nowrap;
}

.verify-btn:active {
  background-color: #4a7c64;
  transform: scale(0.95);
}

.verify-btn.disabled {
  background-color: #a0aec0;
  color: white;
  opacity: 0.6;
  pointer-events: none;
}

.password-toggle {
  padding: 8rpx;
  color: #718096;
  transition: all 0.2s ease;
}

.password-toggle:active {
  color: #5a9178;
  transform: scale(0.95);
}

.eye-icon {
  width: 32rpx;
  height: 32rpx;
}

.error-msg {
  color: #ff6b6b;
  font-size: 24rpx;
  margin-top: 8rpx;
  padding: 12rpx 16rpx;
  background-color: rgba(255, 107, 107, 0.1);
  border-radius: 8rpx;
  border-left: 4rpx solid #ff6b6b;
}

/* 协议勾选 */
.agreement-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.checkbox-container {
  padding: 4rpx;
  margin-right: 12rpx;
}

.checkbox {
  width: 28rpx;
  height: 28rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
  background-color: white;
}

.checkbox.checked {
  background-color: #5a9178;
  border-color: #5a9178;
  color: white;
}

.checkbox.checked::after {
  content: '✓';
  font-size: 18rpx;
  font-weight: 700;
}

.agreement-text {
  font-size: 24rpx;
  color: #4a5568;
  flex: 1;
  line-height: 1.5;
}

.link {
  color: #5a9178;
  font-weight: 600;
  text-decoration: underline;
}

/* 注册按钮 */
.register-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(135deg, #5a9178 0%, #4a7c64 100%);
  color: white;
  font-size: 30rpx;
  font-weight: 700;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 20rpx rgba(90, 145, 120, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: none;
}

.register-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.register-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(90, 145, 120, 0.4);
}

.register-btn:active::before {
  left: 100%;
}

.register-btn.loading {
  opacity: 0.8;
  pointer-events: none;
}

.register-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid rgba(255,255,255,0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 登录链接 */
.login-link {
  text-align: center;
  font-size: 26rpx;
  color: #718096;
  margin-bottom: 20rpx;
}

.login-link text {
  color: #5a9178;
  font-weight: 600;
  text-decoration: underline;
}

/* 教练注册说明 */
.coach-tips {
  margin-top: 24rpx;
  padding: 20rpx;
  background-color: rgba(90, 145, 120, 0.08);
  border-radius: 16rpx;
  border: 1rpx solid rgba(90, 145, 120, 0.2);
}

.tips-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  color: #5a9178;
  font-weight: 700;
}

.tips-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.tips-content {
  font-size: 24rpx;
  color: #4a5568;
  line-height: 1.6;
}

.tips-content text {
  display: block;
  margin-bottom: 8rpx;
  position: relative;
  padding-left: 16rpx;
}

.tips-content text::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #5a9178;
  font-weight: 700;
}

/* 底部安全区域 */
.safe-bottom {
  height: env(safe-area-inset-bottom);
  background-color: transparent;
}

/* 用戶類型按鈕微交互效果 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2rpx); }
  20%, 40%, 60%, 80% { transform: translateX(2rpx); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4rpx);
  }
  60% {
    transform: translateY(-2rpx);
  }
}

/* 移除複雜動畫效果以符合扁平化設計 */

/* 錯誤狀態動畫 */
.type-item.error {
  animation: shake 0.5s ease-in-out;
  border-color: #ff6b6b;
  box-shadow: 0 0 0 4rpx rgba(255, 107, 107, 0.1);
}

/* 響應式調整 */
@media (max-width: 480px) {
  .type-item {
    max-width: 220rpx;
    height: 80rpx;
    padding: 0 16rpx;
  }
  
  .type-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
  
  .type-text {
    font-size: 26rpx;
  }
}