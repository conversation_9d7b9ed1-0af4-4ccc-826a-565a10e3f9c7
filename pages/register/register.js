// pages/register/register.js
const app = getApp()

Page({
  data: {
    phone: '',
    password: '',
    confirmPassword: '',
    verifyCode: '',
    showPassword: false,
    showConfirmPassword: false,
    countdown: 0,
    isLoading: false,
    errorMsg: '',
    isAgreement: false,
    userType: 'student', // 'student' 或 'coach'
  },

  onLoad(options) {
    // 如果有推荐人，可以在这里处理
    if (options.referrer) {
      // 保存推荐人信息
    }
  },

  // 输入手机号
  inputPhone(e) {
    this.setData({
      phone: e.detail.value,
      errorMsg: ''
    })
  },

  // 输入密码
  inputPassword(e) {
    this.setData({
      password: e.detail.value,
      errorMsg: ''
    })
  },

  // 输入确认密码
  inputConfirmPassword(e) {
    this.setData({
      confirmPassword: e.detail.value,
      errorMsg: ''
    })
  },

  // 输入验证码
  inputVerifyCode(e) {
    this.setData({
      verifyCode: e.detail.value,
      errorMsg: ''
    })
  },

  // 切换显示/隐藏密码
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },

  // 切换显示/隐藏确认密码
  toggleConfirmPasswordVisibility() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword
    })
  },

  // 切换协议同意状态
  toggleAgreement() {
    this.setData({
      isAgreement: !this.data.isAgreement
    })
  },

  // 切换用户类型
  switchUserType(e) {
    this.setData({
      userType: e.currentTarget.dataset.type
    })
  },

  // 获取验证码
  getVerifyCode() {
    const { phone, countdown } = this.data
    
    if (countdown > 0) return
    
    if (!phone) {
      this.setData({ errorMsg: '请输入手机号' })
      return
    }
    
    if (!/^1\d{10}$/.test(phone)) {
      this.setData({ errorMsg: '请输入正确的手机号' })
      return
    }
    
    // 开始倒计时
    this.setData({ countdown: 60 })
    const timer = setInterval(() => {
      if (this.data.countdown <= 1) {
        clearInterval(timer)
        this.setData({ countdown: 0 })
      } else {
        this.setData({ countdown: this.data.countdown - 1 })
      }
    }, 1000)
    
    // 模拟发送验证码
    wx.showToast({
      title: '验证码已发送',
      icon: 'success'
    })
    
    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/send-verify-code`,
      method: 'POST',
      data: {
        phone: phone
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          wx.showToast({
            title: '验证码已发送',
            icon: 'success'
          })
        } else {
          this.setData({ 
            errorMsg: res.data.message || '发送验证码失败',
            countdown: 0
          })
          clearInterval(timer)
        }
      },
      fail: () => {
        this.setData({ 
          errorMsg: '网络错误，请稍后重试',
          countdown: 0
        })
        clearInterval(timer)
      }
    })
    */
  },

  // 验证表单
  validateForm() {
    const { phone, password, confirmPassword, verifyCode, isAgreement } = this.data
    
    if (!phone) {
      this.setData({ errorMsg: '请输入手机号' })
      return false
    }
    
    if (!/^1\d{10}$/.test(phone)) {
      this.setData({ errorMsg: '请输入正确的手机号' })
      return false
    }
    
    if (!verifyCode) {
      this.setData({ errorMsg: '请输入验证码' })
      return false
    }
    
    if (!password) {
      this.setData({ errorMsg: '请输入密码' })
      return false
    }
    
    if (password.length < 6) {
      this.setData({ errorMsg: '密码长度不能少于6位' })
      return false
    }
    
    if (password !== confirmPassword) {
      this.setData({ errorMsg: '两次输入的密码不一致' })
      return false
    }
    
    if (!isAgreement) {
      this.setData({ errorMsg: '请阅读并同意用户协议和隐私政策' })
      return false
    }
    
    return true
  },

  // 注册
  handleRegister() {
    if (!this.validateForm()) return
    
    const { phone, password, verifyCode, userType } = this.data
    
    this.setData({ isLoading: true })
    
    // 模拟注册请求
    setTimeout(() => {
      // 模拟注册成功
      const mockUserInfo = {
        id: Date.now(), // 使用时间戳作为用户ID
        nickName: phone.substring(0, 3) + '****' + phone.substring(7),
        avatarUrl: '/images/default-avatar.png',
        phone: phone,
        userType: userType // 直接保存用户选择的类型
      }
      
      // 保存用户信息和token到全局和本地存储
      app.globalData.userInfo = mockUserInfo
      app.globalData.isLogin = true
      app.globalData.userType = userType
      
      wx.setStorageSync('token', 'mock-token-' + Date.now())
      wx.setStorageSync('userInfo', mockUserInfo)
      wx.setStorageSync('userType', userType) // 保存用户注册时选择的类型
      
      this.setData({ isLoading: false })
      
      wx.showToast({
        title: '注册成功',
        icon: 'success'
      })
      
      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index',
        })
      }, 1500)
    }, 1000)
    
    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/register`,
      method: 'POST',
      data: {
        phone: phone,
        password: password,
        verifyCode: verifyCode,
        userType: userType
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          // 保存用户信息和token
          app.globalData.userInfo = res.data.data.userInfo
          app.globalData.isCoach = res.data.data.userInfo.isCoach
          wx.setStorageSync('token', res.data.data.token)
          wx.setStorageSync('userInfo', res.data.data.userInfo)
          
          wx.showToast({
            title: '注册成功',
            icon: 'success'
          })
          
          // 跳转到首页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index',
            })
          }, 1500)
        } else {
          this.setData({ 
            errorMsg: res.data.message || '注册失败',
            isLoading: false
          })
        }
      },
      fail: () => {
        this.setData({ 
          errorMsg: '网络错误，请稍后重试',
          isLoading: false
        })
      }
    })
    */
  },

  // 跳转到登录页面
  goToLogin() {
    wx.navigateBack()
  },

  // 跳转到用户协议页面
  goToAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/index',
    })
  },

  // 跳转到隐私政策页面
  goToPrivacy() {
    wx.navigateTo({
      url: '/pages/privacy/index',
    })
  }
})