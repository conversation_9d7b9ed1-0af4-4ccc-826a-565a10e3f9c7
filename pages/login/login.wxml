<!--pages/login/login.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="logo">台</view>
    <view class="app-title">欢迎来到台球教练</view>
    <view class="app-subtitle">登录后使用完整功能</view>
  </view>

  <!-- 登录面板 -->
  <view class="login-panel">
    <!-- 选项卡 -->
    <view class="tab-container">
      <view class="tab-item {{currentTab === 'phone' ? 'active' : ''}}" bindtap="switchTab" data-tab="phone">
        <text class="tab-text">手机号登录</text>
        <view class="tab-line" wx:if="{{currentTab === 'phone'}}"></view>
      </view>
      <view class="tab-item {{currentTab === 'wechat' ? 'active' : ''}}" bindtap="switchTab" data-tab="wechat">
        <text class="tab-text">微信登录</text>
        <view class="tab-line" wx:if="{{currentTab === 'wechat'}}"></view>
      </view>
    </view>

    <!-- 手机号登录 -->
    <view class="phone-login-form" wx:if="{{currentTab === 'phone'}}">
      <view class="form-group {{phoneFocus ? 'focus' : ''}}">
        <text class="country-code">+86</text>
        <input 
          class="input" 
          type="number" 
          placeholder="请输入手机号" 
          maxlength="11" 
          value="{{phone}}" 
          bindinput="inputPhone"
          bindfocus="onPhoneFocus"
          bindblur="onPhoneBlur"
        />
      </view>

      <view class="form-group {{codeFocus ? 'focus' : ''}}" style="position: relative;">
        <input 
          class="input" 
          type="number" 
          placeholder="请输入验证码" 
          maxlength="6" 
          value="{{code}}" 
          bindinput="inputCode"
          bindfocus="onCodeFocus"
          bindblur="onCodeBlur"
        />
        <button class="get-code-btn {{!canGetCode ? 'disabled' : ''}}" bindtap="getCode" disabled="{{!canGetCode}}">
          {{codeText}}
        </button>
      </view>

      <button class="login-btn {{!canLogin ? 'disabled' : ''}}" bindtap="phoneLogin" disabled="{{!canLogin}}">
        登录
      </button>
    </view>

    <!-- 微信登录 -->
    <view class="wechat-login-form" wx:if="{{currentTab === 'wechat'}}">
      <button class="wechat-login-btn" open-type="getUserInfo" bindgetuserinfo="wechatLogin">
        <image class="wechat-icon" src="/images/wechat.png"></image>
        微信登录
      </button>
    </view>

    <!-- 协议 -->
    <view class="agreement-section">
      <label class="checkbox-label" bindtap="toggleAgreement">
        <checkbox class="checkbox" checked="{{isAgreement}}" />
        <text class="agreement-text">登录即表示同意<text class="link" bindtap="goToAgreement" catchtap="goToAgreement">《用户协议》</text>和<text class="link" bindtap="goToPrivacy" catchtap="goToPrivacy">《隐私政策》</text></text>
      </label>
    </view>
  </view>

  <!-- 底部帮助 -->
  <view class="footer">
    <text class="help-text">登录遇到问题？</text>
    <text class="help-link" bindtap="contactService">联系客服</text>
  </view>
</view>