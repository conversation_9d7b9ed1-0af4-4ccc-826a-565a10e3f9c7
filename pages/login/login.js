// pages/login/login.js
const app = getApp()

Page({
  data: {
    currentTab: 'phone', // 当前选项卡: phone | wechat
    phone: '',
    code: '',
    phoneFocus: false,
    codeFocus: false,
    isAgreement: false,
    canGetCode: false,
    canLogin: false,
    codeText: '获取验证码',
    countdown: 0
  },

  onLoad(options) {
    // 检查是否已登录
    const token = wx.getStorageSync('token')
    if (token) {
      // 已登录，跳转到首页
      wx.switchTab({
        url: '/pages/index/index',
      })
    }
  },

  // 切换选项卡
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      currentTab: tab
    })
  },

  // 输入手机号
  inputPhone(e) {
    const phone = e.detail.value
    this.setData({
      phone: phone
    })
    this.checkCanGetCode()
    this.checkCanLogin()
  },

  // 输入验证码
  inputCode(e) {
    const code = e.detail.value
    this.setData({
      code: code
    })
    this.checkCanLogin()
  },

  // 手机号输入框焦点
  onPhoneFocus() {
    this.setData({ phoneFocus: true })
  },

  onPhoneBlur() {
    this.setData({ phoneFocus: false })
  },

  // 验证码输入框焦点
  onCodeFocus() {
    this.setData({ codeFocus: true })
  },

  onCodeBlur() {
    this.setData({ codeFocus: false })
  },

  // 检查是否可以获取验证码
  checkCanGetCode() {
    const { phone, countdown } = this.data
    const canGetCode = /^1\d{10}$/.test(phone) && countdown === 0
    this.setData({ canGetCode })
  },

  // 检查是否可以登录
  checkCanLogin() {
    const { phone, code, isAgreement } = this.data
    const canLogin = /^1\d{10}$/.test(phone) && code.length >= 4 && isAgreement
    this.setData({ canLogin })
  },

  // 获取验证码
  getCode() {
    if (!this.data.canGetCode) return

    // 模拟发送验证码
    wx.showLoading({ title: '发送中...' })
    
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      })
      
      // 开始倒计时
      this.startCountdown()
    }, 1000)

    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/send-code`,
      method: 'POST',
      data: {
        phone: this.data.phone
      },
      success: (res) => {
        wx.hideLoading()
        if (res.statusCode === 200 && res.data.success) {
          wx.showToast({
            title: '验证码已发送',
            icon: 'success'
          })
          this.startCountdown()
        } else {
          wx.showToast({
            title: res.data.message || '发送失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
    */
  },

  // 模拟从服务器获取用户类型（实际项目中应该调用真实API）
  getUserTypeFromServer(phone) {
    // 这里是模拟逻辑，实际项目中应该调用API获取用户真实身份
    // 为了演示，我们根据手机号尾数来判断：偶数为学员，奇数为教练
    const lastDigit = parseInt(phone.substr(-1));
    return lastDigit % 2 === 0 ? 'student' : 'coach';
    
    /* 实际API调用示例：
    wx.request({
      url: `${app.globalData.baseUrl}/api/get-user-type`,
      method: 'POST',
      data: { phone: phone },
      success: (res) => {
        return res.data.userType;
      }
    });
    */
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    this.setData({
      countdown: countdown,
      codeText: `${countdown}s后重发`
    })

    const timer = setInterval(() => {
      countdown--
      if (countdown > 0) {
        this.setData({
          countdown: countdown,
          codeText: `${countdown}s后重发`
        })
      } else {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          codeText: '获取验证码'
        })
        this.checkCanGetCode()
      }
    }, 1000)
  },

  // 手机号登录
  phoneLogin() {
    if (!this.data.canLogin) return

    const { phone, code } = this.data

    wx.showLoading({ title: '登录中...' })
    
    // 模拟登录请求
    setTimeout(() => {
      wx.hideLoading()
      
      // 模拟从服务器获取用户信息（包含身份类型）
      const mockUserInfo = {
        id: 1,
        nickName: '用户' + phone.substr(-4),
        avatarUrl: '/images/default-avatar.png',
        phone: phone,
        // 实际项目中这个信息应该从服务器返回
        userType: this.getUserTypeFromServer(phone) // 根据手机号从服务器获取用户类型
      }
      
      // 保存用户信息和token
      app.globalData.userInfo = mockUserInfo
      app.globalData.isLogin = true
      app.globalData.userType = mockUserInfo.userType
      
      wx.setStorageSync('token', 'mock-token-' + Date.now())
      wx.setStorageSync('userInfo', mockUserInfo)
      wx.setStorageSync('userType', mockUserInfo.userType) // 保存从服务器获取的用户类型
      
      // 跳转到首页
      wx.switchTab({
        url: '/pages/index/index',
      })
      
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })
    }, 1500)

    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/login-by-code`,
      method: 'POST',
      data: {
        phone: phone,
        code: code
      },
      success: (res) => {
        wx.hideLoading()
        if (res.statusCode === 200 && res.data.success) {
          // 保存用户信息和token
          app.globalData.userInfo = res.data.data.userInfo
          app.globalData.isLogin = true
          wx.setStorageSync('token', res.data.data.token)
          wx.setStorageSync('userInfo', res.data.data.userInfo)
          
          // 跳转到首页
          wx.switchTab({
            url: '/pages/index/index',
          })
          
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          })
        } else {
          wx.showToast({
            title: res.data.message || '登录失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
    */
  },

  // 微信登录
  wechatLogin(e) {
    if (!this.data.isAgreement) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    const userInfo = e.detail.userInfo
    if (!userInfo) {
      wx.showToast({
        title: '登录已取消',
        icon: 'none'
      })
      return
    }

    wx.showLoading({ title: '登录中...' })

    // 获取微信登录code
    wx.login({
      success: (res) => {
        if (res.code) {
          // 模拟微信登录
          setTimeout(() => {
            wx.hideLoading()
            
            // 保存用户信息
            app.globalData.userInfo = userInfo
            app.globalData.isLogin = true
            wx.setStorageSync('token', 'wechat-token-' + Date.now())
            wx.setStorageSync('userInfo', userInfo)
            
            // 跳转到首页
            wx.switchTab({
              url: '/pages/index/index',
            })
            
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            })
          }, 1500)

          // 实际API请求示例
          /*
          wx.request({
            url: `${app.globalData.baseUrl}/api/wechat-login`,
            method: 'POST',
            data: {
              code: res.code,
              userInfo: userInfo
            },
            success: (res) => {
              wx.hideLoading()
              if (res.statusCode === 200 && res.data.success) {
                app.globalData.userInfo = res.data.data.userInfo
                app.globalData.isLogin = true
                wx.setStorageSync('token', res.data.data.token)
                wx.setStorageSync('userInfo', res.data.data.userInfo)
                
                wx.switchTab({
                  url: '/pages/index/index',
                })
                
                wx.showToast({
                  title: '登录成功',
                  icon: 'success'
                })
              } else {
                wx.showToast({
                  title: res.data.message || '登录失败',
                  icon: 'none'
                })
              }
            },
            fail: () => {
              wx.hideLoading()
              wx.showToast({
                title: '网络错误',
                icon: 'none'
              })
            }
          })
          */
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '微信登录失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '微信登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 切换协议同意状态
  toggleAgreement() {
    const isAgreement = !this.data.isAgreement
    this.setData({ isAgreement })
    this.checkCanLogin()
  },

  // 跳转到用户协议页面
  goToAgreement(e) {
    e.stopPropagation()
    wx.navigateTo({
      url: '/pages/agreement/user',
    })
  },

  // 跳转到隐私政策页面
  goToPrivacy(e) {
    e.stopPropagation()
    wx.navigateTo({
      url: '/pages/agreement/privacy',
    })
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    })
  }
})