<!--pages/login/forget.wxml-->
<view class="container">
  <view class="forget-header">
    <text class="page-title">{{step === 1 ? '找回密码' : '重置密码'}}</text>
    <text class="page-subtitle">{{step === 1 ? '验证手机号码' : '设置新密码'}}</text>
  </view>
  
  <!-- 步骤指示器 -->
  <view class="step-indicator">
    <view class="step-item {{step >= 1 ? 'active' : ''}}">
      <view class="step-number">1</view>
      <view class="step-label">验证手机</view>
    </view>
    <view class="step-line {{step >= 2 ? 'active' : ''}}"></view>
    <view class="step-item {{step >= 2 ? 'active' : ''}}">
      <view class="step-number">2</view>
      <view class="step-label">重置密码</view>
    </view>
  </view>
  
  <!-- 步骤1：验证手机号 -->
  <view class="forget-form" wx:if="{{step === 1}}">
    <!-- 手机号输入 -->
    <view class="form-item">
      <view class="input-container">
        <image class="input-icon" src="/images/icon-phone.png"></image>
        <input 
          class="input" 
          type="number" 
          placeholder="请输入手机号" 
          maxlength="11" 
          value="{{phone}}" 
          bindinput="inputPhone"
        />
      </view>
    </view>
    
    <!-- 验证码输入 -->
    <view class="form-item">
      <view class="input-container">
        <image class="input-icon" src="/images/icon-verify.png"></image>
        <input 
          class="input" 
          type="number" 
          placeholder="请输入验证码" 
          maxlength="6" 
          value="{{verifyCode}}" 
          bindinput="inputVerifyCode"
        />
        <view 
          class="verify-btn {{countdown > 0 ? 'disabled' : ''}}" 
          bindtap="getVerifyCode"
        >
          {{countdown > 0 ? countdown + 's' : '获取验证码'}}
        </view>
      </view>
    </view>
    
    <!-- 错误信息 -->
    <view class="error-msg" wx:if="{{errorMsg}}">{{errorMsg}}</view>
    
    <!-- 下一步按钮 -->
    <button 
      class="next-btn {{isLoading ? 'loading' : ''}}" 
      bindtap="verifyPhone" 
      disabled="{{isLoading}}"
    >
      {{isLoading ? '验证中...' : '下一步'}}
    </button>
  </view>
  
  <!-- 步骤2：设置新密码 -->
  <view class="forget-form" wx:if="{{step === 2}}">
    <!-- 密码输入 -->
    <view class="form-item">
      <view class="input-container">
        <image class="input-icon" src="/images/icon-password.png"></image>
        <input 
          class="input" 
          password="{{!showPassword}}" 
          placeholder="请设置新密码" 
          value="{{password}}" 
          bindinput="inputPassword"
        />
        <view class="password-toggle" bindtap="togglePasswordVisibility">
          <image class="eye-icon" src="{{showPassword ? '/images/eye-open.png' : '/images/eye-close.png'}}"></image>
        </view>
      </view>
    </view>
    
    <!-- 确认密码输入 -->
    <view class="form-item">
      <view class="input-container">
        <image class="input-icon" src="/images/icon-password.png"></image>
        <input 
          class="input" 
          password="{{!showConfirmPassword}}" 
          placeholder="请确认新密码" 
          value="{{confirmPassword}}" 
          bindinput="inputConfirmPassword"
        />
        <view class="password-toggle" bindtap="toggleConfirmPasswordVisibility">
          <image class="eye-icon" src="{{showConfirmPassword ? '/images/eye-open.png' : '/images/eye-close.png'}}"></image>
        </view>
      </view>
    </view>
    
    <!-- 错误信息 -->
    <view class="error-msg" wx:if="{{errorMsg}}">{{errorMsg}}</view>
    
    <!-- 提交按钮 -->
    <button 
      class="submit-btn {{isLoading ? 'loading' : ''}}" 
      bindtap="resetPassword" 
      disabled="{{isLoading}}"
    >
      {{isLoading ? '提交中...' : '提交'}}
    </button>
  </view>
  
  <!-- 返回登录 -->
  <view class="back-to-login" bindtap="goToLogin">返回登录</view>
</view>