// pages/login/forget.js
const app = getApp()

Page({
  data: {
    step: 1, // 1: 验证手机号, 2: 设置新密码
    phone: '',
    verifyCode: '',
    password: '',
    confirmPassword: '',
    showPassword: false,
    showConfirmPassword: false,
    countdown: 0,
    isLoading: false,
    errorMsg: ''
  },

  // 输入手机号
  inputPhone(e) {
    this.setData({
      phone: e.detail.value,
      errorMsg: ''
    })
  },

  // 输入验证码
  inputVerifyCode(e) {
    this.setData({
      verifyCode: e.detail.value,
      errorMsg: ''
    })
  },

  // 输入密码
  inputPassword(e) {
    this.setData({
      password: e.detail.value,
      errorMsg: ''
    })
  },

  // 输入确认密码
  inputConfirmPassword(e) {
    this.setData({
      confirmPassword: e.detail.value,
      errorMsg: ''
    })
  },

  // 切换显示/隐藏密码
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },

  // 切换显示/隐藏确认密码
  toggleConfirmPasswordVisibility() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword
    })
  },

  // 获取验证码
  getVerifyCode() {
    const { phone, countdown } = this.data
    
    if (countdown > 0) return
    
    if (!phone) {
      this.setData({ errorMsg: '请输入手机号' })
      return
    }
    
    if (!/^1\d{10}$/.test(phone)) {
      this.setData({ errorMsg: '请输入正确的手机号' })
      return
    }
    
    // 开始倒计时
    this.setData({ countdown: 60 })
    const timer = setInterval(() => {
      if (this.data.countdown <= 1) {
        clearInterval(timer)
        this.setData({ countdown: 0 })
      } else {
        this.setData({ countdown: this.data.countdown - 1 })
      }
    }, 1000)
    
    // 模拟发送验证码
    wx.showToast({
      title: '验证码已发送',
      icon: 'success'
    })
    
    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/send-verify-code`,
      method: 'POST',
      data: {
        phone: phone,
        type: 'reset-password'
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          wx.showToast({
            title: '验证码已发送',
            icon: 'success'
          })
        } else {
          this.setData({ 
            errorMsg: res.data.message || '发送验证码失败',
            countdown: 0
          })
          clearInterval(timer)
        }
      },
      fail: () => {
        this.setData({ 
          errorMsg: '网络错误，请稍后重试',
          countdown: 0
        })
        clearInterval(timer)
      }
    })
    */
  },

  // 验证手机号和验证码
  validateStep1() {
    const { phone, verifyCode } = this.data
    
    if (!phone) {
      this.setData({ errorMsg: '请输入手机号' })
      return false
    }
    
    if (!/^1\d{10}$/.test(phone)) {
      this.setData({ errorMsg: '请输入正确的手机号' })
      return false
    }
    
    if (!verifyCode) {
      this.setData({ errorMsg: '请输入验证码' })
      return false
    }
    
    return true
  },

  // 验证密码
  validateStep2() {
    const { password, confirmPassword } = this.data
    
    if (!password) {
      this.setData({ errorMsg: '请输入新密码' })
      return false
    }
    
    if (password.length < 6) {
      this.setData({ errorMsg: '密码长度不能少于6位' })
      return false
    }
    
    if (!confirmPassword) {
      this.setData({ errorMsg: '请确认新密码' })
      return false
    }
    
    if (password !== confirmPassword) {
      this.setData({ errorMsg: '两次输入的密码不一致' })
      return false
    }
    
    return true
  },

  // 验证手机号和验证码
  verifyPhone() {
    if (!this.validateStep1()) return
    
    const { phone, verifyCode } = this.data
    
    this.setData({ isLoading: true })
    
    // 模拟验证请求
    setTimeout(() => {
      this.setData({ 
        isLoading: false,
        step: 2,
        errorMsg: ''
      })
    }, 1000)
    
    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/verify-code`,
      method: 'POST',
      data: {
        phone: phone,
        code: verifyCode,
        type: 'reset-password'
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          this.setData({ 
            step: 2,
            errorMsg: '',
            isLoading: false
          })
        } else {
          this.setData({ 
            errorMsg: res.data.message || '验证码错误',
            isLoading: false
          })
        }
      },
      fail: () => {
        this.setData({ 
          errorMsg: '网络错误，请稍后重试',
          isLoading: false
        })
      }
    })
    */
  },

  // 重置密码
  resetPassword() {
    if (!this.validateStep2()) return
    
    const { phone, verifyCode, password } = this.data
    
    this.setData({ isLoading: true })
    
    // 模拟重置密码请求
    setTimeout(() => {
      this.setData({ isLoading: false })
      
      wx.showToast({
        title: '密码重置成功',
        icon: 'success'
      })
      
      // 返回登录页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }, 1000)
    
    // 实际API请求示例
    /*
    wx.request({
      url: `${app.globalData.baseUrl}/api/reset-password`,
      method: 'POST',
      data: {
        phone: phone,
        code: verifyCode,
        password: password
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          wx.showToast({
            title: '密码重置成功',
            icon: 'success'
          })
          
          // 返回登录页
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          this.setData({ 
            errorMsg: res.data.message || '密码重置失败',
            isLoading: false
          })
        }
      },
      fail: () => {
        this.setData({ 
          errorMsg: '网络错误，请稍后重试',
          isLoading: false
        })
      }
    })
    */
  },

  // 返回登录页
  goToLogin() {
    wx.navigateBack()
  }
})