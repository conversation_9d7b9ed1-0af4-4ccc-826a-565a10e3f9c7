/* pages/login/login.wxss */

page {
  background-color: #f7fafc;
}

.container {
  min-height: 100vh;
  padding: 80rpx 32rpx;
  box-sizing: border-box;
  background: linear-gradient(180deg, #5a9178 0%, #63a083 30%, #f7fafc 60%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 64rpx;
  color: white;
  width: 100%;
}

.logo {
  width: 140rpx;
  height: 140rpx;
  line-height: 140rpx;
  background-color: white;
  color: #5a9178;
  border-radius: 50%;
  font-size: 80rpx;
  font-weight: bold;
  margin: 0 auto 32rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}

.app-title {
  font-size: 40rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.app-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 登录面板 */
.login-panel {
  width: 90%;
  max-width: 640rpx;
  background-color: white;
  border-radius: 24rpx;
  padding: 48rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin: 0 auto 0 auto;
  flex-shrink: 0;
}

/* 选项卡 */
.tab-container {
  display: flex;
  margin-bottom: 48rpx;
  position: relative;
}

.tab-item {
  flex: 1;
  text-align: center;
  position: relative;
  padding-bottom: 24rpx;
}

.tab-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #718096;
  transition: all 0.3s ease;
}

.tab-item.active .tab-text {
  color: #5a9178;
  font-weight: 600;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #5a9178;
  border-radius: 3rpx;
}

/* 手机号登录表单 */
.phone-login-form {
  width: 100%;
  max-width: 560rpx;
  margin: 0 auto;
}

.form-group {
  position: relative;
  display: flex;
  align-items: center;
  height: 96rpx;
  margin-bottom: 32rpx;
  background-color: #f7fafc;
  border-radius: 16rpx;
  border: 2rpx solid #e2e8f0;
  padding: 0 32rpx;
  transition: all 0.2s ease;
}

.form-group.focus {
  border-color: #5a9178;
  box-shadow: 0 0 0 4rpx rgba(90, 145, 120, 0.1);
}

.country-code {
  font-size: 28rpx;
  color: #2d3748;
  padding-right: 16rpx;
  border-right: 1rpx solid #e2e8f0;
  margin-right: 16rpx;
}

.input {
  flex: 1;
  font-size: 28rpx;
  height: 100%;
  color: #2d3748;
}

.input::placeholder {
  color: #a0aec0;
}

.get-code-btn {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  height: 64rpx;
  padding: 0 24rpx;
  background-color: #5a9178;
  color: white;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.2s ease;
}

.get-code-btn::after {
  border: none;
}

.get-code-btn:active {
  background-color: #4a7c64;
}

.get-code-btn.disabled {
  background-color: #e2e8f0;
  color: #a0aec0;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  background-color: #5a9178;
  color: white;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 16rpx rgba(90, 145, 120, 0.2);
  margin-top: 32rpx;
  border: none;
  transition: all 0.2s ease;
}

.login-btn::after {
  border: none;
}

.login-btn:active {
  background-color: #4a7c64;
}

.login-btn.disabled {
  background-color: #e2e8f0;
  color: #a0aec0;
  box-shadow: none;
}

/* 微信登录 */
.wechat-login-form {
  width: 100%;
  max-width: 560rpx;
  margin: 0 auto;
  padding: 48rpx 0;
  text-align: center;
}

.wechat-login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 96rpx;
  background-color: #5a9178;
  color: white;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 16rpx rgba(90, 145, 120, 0.2);
  border: none;
  transition: all 0.2s ease;
  line-height: 96rpx; /* 确保文字垂直居中 */
  padding: 0; /* 移除内边距 */
}

.wechat-login-btn::after {
  border: none;
}

.wechat-login-btn:active {
  background-color: #4a7c64;
}

.wechat-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

/* 协议 */
.agreement-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 48rpx;
  border-top: 1rpx solid #e2e8f0;
  margin-top: 48rpx;
  max-width: 560rpx;
  margin-left: auto;
  margin-right: auto;
}

.checkbox-label {
  display: flex;
  align-items: center;
  transform: scale(0.9);
  justify-content: center;
}

/* 重置微信 checkbox 样式 */
.checkbox .wx-checkbox-input {
  width: 32rpx !important;
  height: 32rpx !important;
  border-radius: 50% !important;
  border-color: #a0aec0 !important;
  margin-right: 16rpx !important;
}

.checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #5a9178 !important;
  border-color: #5a9178 !important;
}

.checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  color: white !important;
  font-size: 28rpx !important;
}

.agreement-text {
  font-size: 24rpx;
  color: #718096;
  line-height: 1.5;
  white-space: nowrap;
}

.agreement-text .link {
  color: #5a9178;
  font-weight: 500;
  text-decoration: underline;
}

/* 底部帮助 */
.footer {
  text-align: center;
  margin: 48rpx -32rpx 0 -32rpx;
  width: calc(100% + 64rpx);
  padding: 0;
}

.help-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-right: 16rpx;
  display: inline;
}

.help-link {
  font-size: 26rpx;
  color: #5a9178;
  background-color: white;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  font-weight: 500;
  display: inline-block;
}