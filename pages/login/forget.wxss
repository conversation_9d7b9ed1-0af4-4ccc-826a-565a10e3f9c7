/* pages/login/forget.wxss */

.container {
  padding: 40rpx;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #fff;
}

/* 忘记密码头部 */
.forget-header {
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.page-subtitle {
  font-size: 28rpx;
  color: #999;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.step-item.active .step-number {
  background-color: #3cc51f;
  color: #fff;
}

.step-label {
  font-size: 24rpx;
  color: #999;
}

.step-item.active .step-label {
  color: #3cc51f;
}

.step-line {
  width: 100rpx;
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 0 20rpx;
  margin-bottom: 30rpx;
}

.step-line.active {
  background-color: #3cc51f;
}

/* 忘记密码表单 */
.forget-form {
  width: 100%;
}

.form-item {
  margin-bottom: 30rpx;
}

.input-container {
  display: flex;
  align-items: center;
  height: 90rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.input-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.input {
  flex: 1;
  height: 90rpx;
  font-size: 30rpx;
}

.verify-btn {
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  background-color: #3cc51f;
  color: #fff;
  font-size: 26rpx;
  border-radius: 30rpx;
}

.verify-btn.disabled {
  background-color: #ccc;
}

.password-toggle {
  padding: 20rpx;
}

.eye-icon {
  width: 40rpx;
  height: 40rpx;
}

.error-msg {
  color: #e64340;
  font-size: 26rpx;
  margin-bottom: 20rpx;
}

/* 按钮样式 */
.next-btn, .submit-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #3cc51f;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin-top: 40rpx;
}

.next-btn.loading, .submit-btn.loading {
  opacity: 0.8;
}

/* 返回登录 */
.back-to-login {
  text-align: center;
  font-size: 28rpx;
  color: #3cc51f;
  margin-top: 40rpx;
}