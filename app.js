// app.js
App({
  onLaunch: function () {
    console.log('App Launch')
    
    // 检查登录状态
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')
    const userType = wx.getStorageSync('userType')
    
    if (token && userInfo) {
      this.globalData.isLogin = true
      this.globalData.userInfo = userInfo
      this.globalData.userType = userType
    }
  },
  
  globalData: {
    userInfo: null,
    isLogin: false,
    userType: '', // 'student' 或 'coach'
    baseUrl: 'https://api.example.com' // 实际的API地址
  }
})