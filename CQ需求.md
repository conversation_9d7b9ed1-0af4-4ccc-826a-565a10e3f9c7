🎱线下台球课程培训预约小程序 - 功能需求文档

⸻

🧑‍💼 用户角色
	1.	教练（Coach）
	2.	学员（Student）

⸻

🔐 用户注册与登录
	•	支持手机号 + 验证码登录/注册
	•	登录后根据身份选择进入不同功能模块（教练端 / 学员端）
	•	用户信息存储与管理（头像、昵称、手机号、身份）

⸻

📋 教练端功能（Coach Dashboard）

1. 个人信息管理
	•	上传头像、姓名、联系方式
	•	图文编辑个人简介（支持富文本格式）
	•	标签/擅长风格添加（如“斯诺克”、“八球”、“初学者友好”等）

2. 课程包管理
	•	新建/编辑课程包（名称、包含课时数、价格、使用有效期）
	•	每课时预计时长、适合学员等级说明
	•	上架/下架课程包

3. 排课管理
	•	设置每周的可预约时间段（日历视图）
	•	可选择球房名称与地址（支持多球房）
	•	限制每日最大预约次数 / 学员数

4. 预约与核销管理
	•	查看每日/每周预约信息（日历/列表视图）
	•	核销功能（扫码核销 / 手动确认）
	•	学员课程使用进度统计

5. 订单与收入查看
	•	课程包订单列表（含状态：已付款/已核销）
	•	提现申请（可选）

⸻

🧑‍🎓 学员端功能（Student Dashboard）

1. 购买课程
	•	浏览教练课程包（筛选条件见搜索功能）
	•	查看课程包详情（价格、内容、适合人群、教练评价）
	•	微信支付完成购买

2. 课程进度
	•	查询已购买课程包的课时使用情况
	•	查看剩余课时、课程有效期

3. 预约排课
	•	查看所选教练的开放时间（日历形式）
	•	一键预约课程，确认后进入预约状态
	•	支持取消/更改（有时间限制）

⸻

🔍 教练搜索功能（全角色可访问）

支持以下维度筛选教练：
	•	所在城市/区域
	•	球房名称（支持模糊搜索）
	•	标签筛选（如“经验丰富”、“业余友好”、“近地铁口”等）
	•	综合排序（人气、评分、价格）

教练卡片展示信息包括：
	•	头像 + 名字 + 简介
	•	所在球房
	•	起始价格
	•	标签
	•	预约按钮

⸻

💳 支付系统
	•	支持微信支付（API接入）
	•	订单生成 → 支付成功 → 更新数据库状态
	•	提供支付记录与电子收据查询

⸻

🛠️ 系统后台管理（可选模块）

（如后续需要管理平台统一管理教练与学员数据）
	•	用户管理（教练 / 学员）
	•	教练审核机制（可选）
	•	投诉与反馈管理
	•	订单/收入统计

⸻

✅ 技术建议（给开发参考）
	•	框架建议：Taro + UniApp / 小程序原生 + Node.js 后端
	•	数据库：MySQL / MongoDB
	•	文件存储：腾讯云 COS / 阿里 OSS
	•	身份验证：JWT + 小程序 session_key
	•	第三方服务：微信支付、地图定位（腾讯地图SDK）、富文本编辑器组件
