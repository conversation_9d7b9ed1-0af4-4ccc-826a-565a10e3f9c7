/**app.wxss**/

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #2d3748;
  background-color: #f7fafc;
  box-sizing: border-box;
}

/* 容器 */
.container {
  min-height: 100vh;
  background-color: #f7fafc;
}

.page-container {
  padding: 24rpx;
}

/* 文本样式 */
.text-primary {
  color: #5a9178;
}

.text-secondary {
  color: #4a5568;
}

.text-light {
  color: #718096;
}

.text-muted {
  color: #a0aec0;
}

.text-danger {
  color: #ff6b6b;
}

.text-warning {
  color: #ffd43b;
}

.text-success {
  color: #51cf66;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: 600;
}

.text-medium {
  font-weight: 500;
}

.text-small {
  font-size: 24rpx;
}

.text-large {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-xxl {
  font-size: 40rpx;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 布局 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

/* 间距系统 */
.m-xs { margin: 8rpx; }
.m-sm { margin: 16rpx; }
.m-md { margin: 24rpx; }
.m-lg { margin: 32rpx; }
.m-xl { margin: 48rpx; }

.mt-xs { margin-top: 8rpx; }
.mt-sm { margin-top: 16rpx; }
.mt-md { margin-top: 24rpx; }
.mt-lg { margin-top: 32rpx; }
.mt-xl { margin-top: 48rpx; }

.mb-xs { margin-bottom: 8rpx; }
.mb-sm { margin-bottom: 16rpx; }
.mb-md { margin-bottom: 24rpx; }
.mb-lg { margin-bottom: 32rpx; }
.mb-xl { margin-bottom: 48rpx; }

.ml-xs { margin-left: 8rpx; }
.ml-sm { margin-left: 16rpx; }
.ml-md { margin-left: 24rpx; }
.ml-lg { margin-left: 32rpx; }

.mr-xs { margin-right: 8rpx; }
.mr-sm { margin-right: 16rpx; }
.mr-md { margin-right: 24rpx; }
.mr-lg { margin-right: 32rpx; }

.p-xs { padding: 8rpx; }
.p-sm { padding: 16rpx; }
.p-md { padding: 24rpx; }
.p-lg { padding: 32rpx; }
.p-xl { padding: 48rpx; }

.pt-xs { padding-top: 8rpx; }
.pt-sm { padding-top: 16rpx; }
.pt-md { padding-top: 24rpx; }
.pt-lg { padding-top: 32rpx; }

.pb-xs { padding-bottom: 8rpx; }
.pb-sm { padding-bottom: 16rpx; }
.pb-md { padding-bottom: 24rpx; }
.pb-lg { padding-bottom: 32rpx; }

.pl-xs { padding-left: 8rpx; }
.pl-sm { padding-left: 16rpx; }
.pl-md { padding-left: 24rpx; }
.pl-lg { padding-left: 32rpx; }

.pr-xs { padding-right: 8rpx; }
.pr-sm { padding-right: 16rpx; }
.pr-md { padding-right: 24rpx; }
.pr-lg { padding-right: 32rpx; }

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 24rpx;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
}

.card-body {
  padding: 16rpx 0;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-top: 24rpx;
  border-top: 1px solid #e2e8f0;
  margin-top: 24rpx;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn::after {
  border: none;
}

.btn-primary {
  background-color: #5a9178;
  color: white;
}

.btn-secondary {
  background-color: #edf2f7;
  color: #4a5568;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #5a9178;
  color: #5a9178;
}

.btn-danger {
  background-color: #ff6b6b;
  color: white;
}

.btn-success {
  background-color: #51cf66;
  color: white;
}

.btn-warning {
  background-color: #ffd43b;
  color: #2d3748;
}

.btn-sm {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.btn-lg {
  padding: 24rpx 48rpx;
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
}

.btn-round {
  border-radius: 50rpx;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 表单样式 */
.form-group {
  margin-bottom: 24rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.form-input {
  width: 100%;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #ffffff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #5a9178;
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: #edf2f7;
  color: #4a5568;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.tag-primary {
  background-color: rgba(90, 145, 120, 0.1);
  color: #5a9178;
}

.tag-success {
  background-color: rgba(81, 207, 102, 0.1);
  color: #51cf66;
}

.tag-warning {
  background-color: rgba(255, 212, 59, 0.1);
  color: #ffd43b;
}

.tag-danger {
  background-color: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: #e2e8f0;
  margin: 24rpx 0;
}

/* 徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40rpx;
  height: 40rpx;
  padding: 0 8rpx;
  background-color: #ff6b6b;
  color: white;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
}

.badge-primary {
  background-color: #5a9178;
}

.badge-success {
  background-color: #51cf66;
}

.badge-warning {
  background-color: #ffd43b;
  color: #2d3748;
}

/* 头像样式 */
.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #edf2f7;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-sm {
  width: 64rpx;
  height: 64rpx;
}

.avatar-md {
  width: 80rpx;
  height: 80rpx;
}

.avatar-lg {
  width: 120rpx;
  height: 120rpx;
}

.avatar-xl {
  width: 160rpx;
  height: 160rpx;
}

.avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e2e8f0;
  border-top: 4rpx solid #5a9178;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64rpx 32rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #718096;
  margin-bottom: 32rpx;
}

/* 图标样式 */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
}

.icon-sm {
  font-size: 32rpx;
}

.icon-md {
  font-size: 40rpx;
}

.icon-lg {
  font-size: 48rpx;
}

/* 星星评分样式 */
.star-rating {
  display: flex;
  align-items: center;
}

.star {
  color: #ddd;
  font-size: 28rpx;
  margin-right: 4rpx;
}

.star.filled {
  color: #ffd700;
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

/* 兼容旧版本间距 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }