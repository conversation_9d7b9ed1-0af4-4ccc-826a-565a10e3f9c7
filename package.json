{"name": "coach-q", "version": "1.0.0", "main": "app.js", "dependencies": {"@vant/weapp": "^1.11.7", "agent-base": "^5.1.1", "async": "^3.2.6", "balanced-match": "^1.0.2", "base64-js": "^1.5.1", "bl": "^4.1.0", "brace-expansion": "^1.1.11", "buffer": "^5.7.1", "buffer-crc32": "^0.2.13", "chownr": "^1.1.4", "concat-map": "^0.0.1", "debug": "^4.4.1", "end-of-stream": "^1.4.4", "extract-zip": "^2.0.1", "fd-slicer": "^1.1.0", "fs-constants": "^1.0.0", "fs.realpath": "^1.0.0", "get-stream": "^5.2.0", "glob": "^7.2.3", "https-proxy-agent": "^4.0.0", "ieee754": "^1.2.1", "inflight": "^1.0.6", "inherits": "^2.0.4", "mime": "^2.6.0", "minimatch": "^3.1.2", "mkdirp-classic": "^0.5.3", "ms": "^2.1.3", "once": "^1.4.0", "path-is-absolute": "^1.0.1", "pend": "^1.2.0", "progress": "^2.0.3", "proxy-from-env": "^1.1.0", "pump": "^3.0.2", "puppeteer": "^3.3.0", "readable-stream": "^3.6.2", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "string_decoder": "^1.3.0", "svgexport": "^0.4.2", "tar-fs": "^2.1.3", "tar-stream": "^2.2.0", "through": "^2.3.8", "unbzip2-stream": "^1.4.3", "undici-types": "^7.8.0", "util-deprecate": "^1.0.2", "wrappy": "^1.0.2", "ws": "^7.5.10", "yauzl": "^2.10.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"sharp": "^0.34.2"}}