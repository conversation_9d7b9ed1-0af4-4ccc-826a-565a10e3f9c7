# Zsh 配置文件

# 啟用顏色支持
autoload -U colors && colors

# 命令歷史配置
HISTSIZE=10000
SAVEHIST=10000
HISTFILE=~/.zsh_history
setopt APPEND_HISTORY
setopt SHARE_HISTORY
setopt HIST_IGNORE_DUPS
setopt HIST_IGNORE_ALL_DUPS

# 自動補全
autoload -U compinit
compinit

# 設置提示符
PS1="%{$fg[green]%}%n@%m%{$reset_color%} %{$fg[blue]%}%~%{$reset_color%} %% "

# 別名設置
alias ll='ls -la'
alias la='ls -A'  
alias l='ls -CF'
alias ..='cd ..'
alias ...='cd ../..'

# Node.js 和 npm 配置
export NODE_OPTIONS="--max-old-space-size=4096"

# 腳本權限設置
umask 022

echo "Zsh 配置已加載" 