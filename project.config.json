{"setting": {"es6": true, "postcss": true, "minified": true, "uglifyFileName": false, "enhance": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": false, "minifyWXML": true}, "compileType": "miniprogram", "libVersion": "3.5.0", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "appid": "wxa59a931db1f663e4", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}